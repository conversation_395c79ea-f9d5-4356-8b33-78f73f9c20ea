// lib/presentation/screens/profile/profile_screen.dart
import 'dart:convert';

import 'package:arabic_sign_language/bloc/auth/auth_bloc.dart';
import 'package:arabic_sign_language/bloc/language/language_bloc.dart';
import 'package:arabic_sign_language/bloc/user/user_bloc.dart';
import 'package:arabic_sign_language/bloc/user/user_event.dart';
import 'package:arabic_sign_language/bloc/user/user_state.dart';
import 'package:arabic_sign_language/common/router.dart';
import 'package:arabic_sign_language/data/models/user/user_model.dart';
import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:country_picker/country_picker.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:image_picker/image_picker.dart';

import '../home/<USER>';

/// Profile screen is now fully Bloc‑driven – no `setState` or Cubit required.
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});
  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final _formKey = GlobalKey<FormState>();

  late final TextEditingController _nameCtl;
  late final TextEditingController _emailCtl;
  late final TextEditingController _phoneCtl;
  late final TextEditingController _countryCtl;
  XFile? pickedFile;
  Uint8List? _pickedImage;
  late UserBloc _userBloc;
  String countryName = '';
  String countryCode = '';
  String countryDialCode = '';

  // Country validation error (not handled by form validation)
  String? _countryError;

  @override
  void initState() {
    super.initState();
    final u = context.read<UserBloc>().userData;
    _nameCtl = TextEditingController(text: u?.name ?? '');
    _emailCtl = TextEditingController(text: u?.email ?? '');
    _phoneCtl =
        TextEditingController(text: u?.mobile?.replaceFirst('+', '') ?? '');
    _countryCtl = TextEditingController(text: u?.nationality ?? '');
    countryName = u?.nationality ?? '';
    _userBloc = context.read<UserBloc>();
  }

  @override
  void dispose() {
    _nameCtl.dispose();
    _emailCtl.dispose();
    _phoneCtl.dispose();
    _countryCtl.dispose();
    _userBloc.add(const ToggleEditProfile(enable: false));
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final userState = context.watch<UserBloc>().state;
    final isSaving = userState is UserUpdating;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              image:
                  DecorationImage(image: AssetImage(APP_BG), fit: BoxFit.cover),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  _topBar(context),
                  const SizedBox(height: 65),
                  Expanded(child: _contentArea(size)),
                ],
              ),
            ),
          ),
          if (isSaving) ...[
            const Opacity(
              opacity: .6,
              child: ModalBarrier(dismissible: false, color: Colors.black),
            ),
            const Center(child: CircularProgressIndicator()),
          ],
        ],
      ),
    );
  }

  // ────────────────────────────────────────────────────────────────────────────
  //  UI helpers
  // ────────────────────────────────────────────────────────────────────────────

  Widget _contentArea(Size size) {
    return BlocConsumer<UserBloc, UserState>(
      listenWhen: (prev, curr) =>
          curr is UserUpdateSuccess || curr is UserUpdateFailure,
      listener: (context, state) {
        if (state is UserUpdateSuccess) {
          // _pickedImage = null;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('profile_update_success'.tr())),
          );
        } else if (state is UserUpdateFailure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message), backgroundColor: Colors.red),
          );
        }
      },
      builder: (context, state) {
        final isEditing = state is UserLoaded && state.isEditing;
        return Stack(
          clipBehavior: Clip.none,
          children: [
            _glassCard(size, isEditing),
            _editSaveButton(isEditing, state is UserUpdating),
            _avatarOverlay(isEditing),
          ],
        );
      },
    );
  }

  Widget _topBar(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          icon: Image.asset(BACK_BUTTON, width: 24, height: 24),
          onPressed: () {
            bool isMatch = currentNavItems.value.any(
              (item) => item.title.toLowerCase() == "Profile".toLowerCase(),
            );
            int index = currentNavItems.value.indexWhere(
              (item) => item.title.toLowerCase() == "Profile".toLowerCase(),
            );
            if (isMatch && index <= 3 && bottomBarIndex.value == 4) {
              Navigator.of(context).pop();
            } else if (isMatch && index <= 3 && bottomBarIndex.value != 0) {
              bottomBarIndex.value = 0;
            } else if (bottomBarIndex.value == 4) {
              bottomBarIndex.value = 0;
            } else {
              Navigator.of(context).pop();
            }
            context
                .read<UserBloc>()
                .add(const ToggleEditProfile(enable: false));
          },
        ),
        // Language toggle button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: BlocBuilder<LanguageBloc, LanguageState>(
            builder: (context, state) {
              return GestureDetector(
                onTap: () {
                  // Toggle language
                  context.read<LanguageBloc>().add(ToggleLanguage());

                  // Set locale based on current language
                  final newLocale = state.locale.languageCode == 'en'
                      ? const Locale('ar')
                      : const Locale('en');

                  context.setLocale(newLocale);

                  // Force rebuild by triggering a state change
                  setState(() {});
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    state.locale.languageCode == 'en' ? 'AR' : 'EN',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _glassCard(Size size, bool isEditing) {
    return Container(
      height: size.height * 0.68,
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.fromLTRB(24, 70, 24, 24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color.fromARGB(150, 128, 103, 255),
            Color.fromARGB(200, 30, 20, 90)
          ],
        ),
        border: Border.all(color: Colors.white24, width: 1.2),
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Form(
              key: _formKey,
              child: Column(
                children: [
                  _inputField(
                    label: 'name'.tr(),
                    controller: _nameCtl,
                    enabled: isEditing,
                    validator: (v) =>
                        (v == null || v.isEmpty) ? 'required'.tr() : null,
                  ),
                  const SizedBox(height: 14),
                  _inputField(
                      label: 'email_id'.tr(),
                      controller: _emailCtl,
                      enabled: false),
                  const SizedBox(height: 14),
                  _inputField(
                    label: 'phone_number'.tr(),
                    controller: _phoneCtl,
                    enabled: isEditing,
                    keyboardType: TextInputType.phone,
                    validator: _validatePhone,
                  ),
                  const SizedBox(height: 14),
                  _buildNationalityField(context, isEditing: isEditing),
                ],
              ),
            ),
            const SizedBox(height: 28),
            _supportAndAboutSection(),
            const SizedBox(height: 24),
            const Divider(color: Colors.white24),
            const SizedBox(height: 20),
            _actionSection(),
          ],
        ),
      ),
    );
  }

  Widget _editSaveButton(bool isEditing, bool isSaving) {
    final isRtl = Directionality.of(context) == TextDirection.RTL;

    return Positioned(
      top: 12,
      right: isRtl ? null : 30,
      left: isRtl ? 30 : null,
      child: IgnorePointer(
        ignoring: isSaving,
        child: IconButton(
          splashRadius: 22,
          icon: Icon(isEditing ? Icons.check : Icons.edit, color: Colors.white),
          onPressed: () {
            final bloc = context.read<UserBloc>();
            final current = bloc.userData; // original values

            if (isEditing) {
              // user is pressing the (save)
              if (_formKey.currentState!.validate()) {
                final updated = UserModel(
                  name: _nameCtl.text.trim(),
                  email: _emailCtl.text.trim(), // read‑only anyway
                  mobile: _phoneCtl.text.trim(),
                  nationality: countryName,
                );

                final hasChanged = current == null
                    ? true
                    : current.name != updated.name ||
                        current.mobile != updated.mobile ||
                        current.nationality != updated.nationality ||
                        _pickedImage != null;

                if (hasChanged) {
                  if (current?.provider == "local") {
                    if (_pickedImage != null) {
                      String? base64Image;
                      base64Image = base64Encode(_pickedImage!);
                      final updatedWithImg = UserModel(
                        name: _nameCtl.text.trim(),
                        email: _emailCtl.text.trim(), // read‑only anyway
                        mobile: _phoneCtl.text.trim(),
                        nationality: _countryCtl.text.trim(),
                      );
                      bloc.add(UpdateUserProfile(updatedWithImg, pickedFile));
                    } else {
                      bloc.add(UpdateUserProfile(updated, null));
                    }
                  } else {
                    bloc.add(UpdateSSOUserProfile(updated));
                  }
                } else {
                  // nothing changed just leave edit mode
                  bloc.add(const ToggleEditProfile(enable: false));
                }
              }
            } else {
              // user is pressing the  (enter edit mode)
              bloc.add(const ToggleEditProfile(enable: true));
            }
          },
        ),
      ),
    );
  }

  Widget _avatarOverlay(bool isEditing) {
    final userData = context.read<UserBloc>().userData;
    Uint8List? imageBytes;
    if (userData?.provider != 'google.com' && userData?.profile_photo != null) {
      imageBytes = base64Decode(userData?.profile_photo ?? "");
    }
    return Positioned(
      top: -60,
      left: 0,
      right: 0,
      child: Column(
        children: [
          Stack(
            alignment: Alignment.bottomRight,
            children: [
              CircleAvatar(
                radius: 60,
                backgroundColor: const Color(0XFF221B67),
                child: userData?.provider != 'google.com'
                    ? _pickedImage == null
                        ? userData?.profile_photo == null
                            ? CircleAvatar(
                                radius: 60,
                                child: Text(
                                  (userData?.name != null &&
                                          userData!.name.isNotEmpty)
                                      ? userData.name
                                          .substring(0, 1)
                                          .toUpperCase()
                                      : 'U',
                                ),
                              )
                            : ClipOval(
                                child: _pickedImage != null
                                    ? Image.memory(_pickedImage!,
                                        fit: BoxFit.cover,
                                        width: 110,
                                        height: 110)
                                    : Image.memory(imageBytes!,
                                        fit: BoxFit.cover,
                                        width: 110,
                                        height: 110),
                              )
                        : ClipOval(
                            child: _pickedImage != null
                                ? Image.memory(_pickedImage!,
                                    fit: BoxFit.cover, width: 110, height: 110)
                                : Image.memory(imageBytes!,
                                    fit: BoxFit.cover, width: 110, height: 110),
                          )
                    : ClipOval(
                        child: Image.network(userData?.profile_photo ?? "",
                            fit: BoxFit.cover, width: 110, height: 110),
                      ),
              ),
              userData?.provider != 'google.com'
                  ? Positioned(
                      bottom: 6,
                      right: 6,
                      child: InkWell(
                        onTap: () {
                          isEditing ? _showImageSourcePicker(context) : null;
                        },
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                              color: isEditing ? Colors.green : Colors.white,
                              shape: BoxShape.circle),
                          child: const Icon(Icons.camera_alt,
                              size: 18, color: Color(0xFF212CB1)),
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
            ],
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  // ────────────────────────────────────────────────────────────────────────────
  //  Sections
  // ────────────────────────────────────────────────────────────────────────────
  Widget _supportAndAboutSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _sectionHeader('support_and_about'.tr()),
        _buildMenuItem(Icons.star_outline, 'my_subscription'.tr(), onTap: () {
          context.pushNamed(AppRoutes.SUBSCRIPTION_ROUTE_NAME, extra: true);
        }),
        _buildMenuItem(Icons.help_outline, 'help_and_support'.tr(), onTap: () {
          context.pushNamed(AppRoutes.HELP_SUPPORT_ROUTE_NAME, extra: true);
        }),
        _buildMenuItem(Icons.description_outlined, 'terms_and_policies'.tr(),
            onTap: () {
          context.pushNamed(AppRoutes.TERMS_POLICIES_ROUTE_NAME);
        }),
      ],
    );
  }

  Widget _actionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _sectionHeader('action'.tr()),
        _buildMenuItem(Icons.report_outlined, 'report_problem'.tr(), onTap: () {
          context.pushNamed(AppRoutes.REPORT_PROBLEM_ROUTE_NAME);
        }),
        _buildMenuItem(Icons.logout, 'logout'.tr(),
            onTap: () => _confirmLogout(context)),
      ],
    );
  }

  // ────────────────────────────────────────────────────────────────────────────
  //  Form field & misc helpers
  // ────────────────────────────────────────────────────────────────────────────
  Widget _buildNationalityField(BuildContext context,
      {required bool isEditing}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'country'.tr(),
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(.7),
          ),
        ),
        const SizedBox(height: 5),
        GestureDetector(
          onTap: isEditing
              ? () {
                  setState(() {
                    _countryError = null; // Clear error when user interacts
                  });
                  showCountryPicker(
                    countryListTheme: const CountryListThemeData(
                      backgroundColor: Color(0xFF212CB1),
                      margin: EdgeInsets.symmetric(horizontal: 10),
                      padding: EdgeInsets.all(10),
                    ),
                    context: context,
                    onSelect: (country) {
                      setState(() {
                        countryName = country.name;
                        _countryError = null;
                        countryCode = country.countryCode;
                        countryDialCode = country.phoneCode;
                        _phoneCtl.text = country.phoneCode;
                      });
                    },
                  );
                }
              : null, // Disable tap if not editing
          child: Container(
            height: 50,
            width: double.infinity,
            decoration: BoxDecoration(
              color: const Color(0XFF221B67),
              borderRadius: BorderRadius.circular(12),
              border: isEditing
                  ? Border.all(color: Colors.green, width: 1)
                  : _countryError != null
                      ? Border.all(color: Colors.red, width: 1)
                      : null,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            alignment: Alignment.centerLeft,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  countryName.isEmpty ? 'select_country'.tr() : countryName,
                  style: TextStyle(
                    color: countryName.isEmpty ? Colors.grey : Colors.white,
                  ),
                ),
                if (isEditing)
                  const Icon(Icons.arrow_drop_down, color: Colors.white),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _inputField({
    required String label,
    required TextEditingController controller,
    bool enabled = false,
    TextInputType? keyboardType,
    FormFieldValidator<String>? validator, // <-- added
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label,
            style:
                TextStyle(fontSize: 12, color: Colors.white.withOpacity(.7))),
        const SizedBox(height: 4),
        Container(
          color: const Color(0XFF221B67),
          child: TextFormField(
            controller: controller,
            enabled: enabled,
            keyboardType: keyboardType,
            validator: validator ??
                (v) => (enabled && (v == null || v.isEmpty))
                    ? 'required'.tr()
                    : null, // fallback default
            style: const TextStyle(
                fontSize: 13, color: Colors.white, fontWeight: FontWeight.w500),
            decoration: InputDecoration(
              disabledBorder: _outline(),
              enabledBorder: _outlineEnabled(),
              focusedBorder: _outlineEnabled(),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
              fillColor: Colors.white.withOpacity(.05),
              filled: true,
              isDense: true,
            ),
          ),
        ),
      ],
    );
  }

  OutlineInputBorder _outline() => OutlineInputBorder(
        borderRadius: BorderRadius.circular(6),
        borderSide: const BorderSide(color: Colors.white24, width: 1),
      );
  OutlineInputBorder _outlineEnabled() => OutlineInputBorder(
        borderRadius: BorderRadius.circular(6),
        borderSide: const BorderSide(color: Colors.green, width: 1),
      );

  Widget _sectionHeader(String text) => Text(text,
      style: const TextStyle(
          fontSize: 16, fontWeight: FontWeight.w600, color: Colors.white));

  Widget _buildMenuItem(IconData icon, String title, {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: Row(children: [
          Icon(icon, color: Colors.white.withOpacity(.9), size: 20),
          const SizedBox(width: 14),
          Text(title,
              style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.w500)),
        ]),
      ),
    );
  }

  // ────────────────────────────────────────────────────────────────────────────
  //  Logout
  // ────────────────────────────────────────────────────────────────────────────
  void _confirmLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text('confirm_logout'.tr()),
        content: Text('logout_confirmation_message'.tr()),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(ctx), child: Text('cancel'.tr())),
          TextButton(
              onPressed: () {
                Navigator.pop(ctx);
                _performLogout(ctx);
              },
              child: Text('logout'.tr(),
                  style: const TextStyle(color: Colors.red))),
        ],
      ),
    );
  }

  void _showImageSourcePicker(BuildContext context) async {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      // backgroundColor: Colors.white,
      builder: (_) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Pick from Gallery'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Take a Photo'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickImage(ImageSource.camera);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    final picker = ImagePicker();
    pickedFile = await picker.pickImage(source: source);
    if (pickedFile != null) {
      final bytes = await pickedFile!.readAsBytes();

      setState(() {
        _pickedImage = bytes;
      });
    }
  }

  Future<void> _performLogout(BuildContext context) async {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => const Center(child: CircularProgressIndicator()));
    try {
      final google = GoogleSignIn();
      if (await google.isSignedIn()) await google.signOut();
      await FirebaseAuth.instance.signOut();
      if (context.mounted && Navigator.canPop(context)) Navigator.pop(context);
      if (context.mounted) {
        context.read<AuthBloc>().add(LogoutRequested());
        context.goNamed(AppRoutes.LOGIN_ROUTE_NAME);
      }
      bottomBarIndex.value = 0;
      showMorePopup.value = false;
    } catch (e) {
      if (context.mounted && Navigator.canPop(context)) Navigator.pop(context);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('${'error_during_logout'.tr()}: $e'),
            backgroundColor: Colors.red));
      }
      if (kDebugMode) print('Error during logout: $e');
    }
  }

  String? _validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }

    // Remove any spaces, dashes, or parentheses for validation
    String cleanedValue = value.trim().replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // Check if it contains only digits
    if (!RegExp(r'^[0-9]+$').hasMatch(cleanedValue)) {
      return 'Phone number can only contain digits';
    }

    // Check minimum length (8 digits)
    if (cleanedValue.length < 8) {
      return 'Phone number must be at least 8 digits';
    }

    // Check maximum length (15 digits - international standard)
    if (cleanedValue.length > 15) {
      return 'Phone number cannot exceed 15 digits';
    }

    // Check if it starts with 0 (common validation for many countries)
    if (cleanedValue.startsWith('0') && cleanedValue.length < 10) {
      return 'Phone number starting with 0 must be at least 10 digits';
    }

    // Additional validation: cannot be all same digits
    if (RegExp(r'^(\d)\1+$').hasMatch(cleanedValue)) {
      return 'Phone number cannot be all same digits';
    }

    // Additional validation: cannot start with common invalid patterns
    if (cleanedValue.startsWith('00') ||
        cleanedValue.startsWith('11') ||
        cleanedValue.startsWith('111')) {
      return 'Please enter a valid phone number';
    }

    return null;
  }
}
