import 'package:arabic_sign_language/bloc/language/language_bloc.dart';
import 'package:arabic_sign_language/bloc/settings/settings_bloc.dart';
import 'package:arabic_sign_language/bloc/settings/settings_event.dart';
import 'package:arabic_sign_language/bloc/settings/settings_state.dart';
import 'package:arabic_sign_language/common/router.dart';
import 'package:arabic_sign_language/data/service/user_service.dart';
import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/widgets.dart' as ui;

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late SettingsBloc _settingsBloc;

  @override
  void initState() {
    super.initState();
    _settingsBloc = SettingsBloc(
      UserService(),
      context.read<LanguageBloc>(),
    );
    _settingsBloc.add(LoadSettings());
  }

  @override
  void dispose() {
    _settingsBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return BlocProvider.value(
      value: _settingsBloc,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: Stack(
          children: [
            Container(
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(APP_BG),
                  fit: BoxFit.cover,
                ),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    _topBar(context),
                    const SizedBox(height: 20),
                    Expanded(child: _contentArea(size)),
                  ],
                ),
              ),
            ),
            BlocBuilder<SettingsBloc, SettingsState>(
              builder: (context, state) {
                if (state is SettingsLoading || state is SettingsSaving) {
                  return Container(
                    color: Colors.black.withOpacity(0.6),
                    child: const Center(
                      child: SizedBox(),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _topBar(BuildContext context) {
    final isRtl = Directionality.of(context) == ui.TextDirection.rtl;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          if (!isRtl) ...[
            IconButton(
              onPressed: () => context.pop(),
              icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
            ),
            const SizedBox(width: 10),
          ],
          Expanded(
            child: Text(
              'settings'.tr(),
              style: Theme.of(context).textTheme.displayLarge?.copyWith(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: isRtl ? TextAlign.right : TextAlign.left,
            ),
          ),
          if (isRtl) ...[
            const SizedBox(width: 10),
            IconButton(
              onPressed: () => context.pop(),
              icon: const Icon(Icons.arrow_forward_ios, color: Colors.white),
            ),
          ],
        ],
      ),
    );
  }

  Widget _contentArea(Size size) {
    return BlocConsumer<SettingsBloc, SettingsState>(
      listener: (context, state) {
        if (state is SettingsError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is SettingsSaved) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.green,
            ),
          );
        }
      },
      builder: (context, state) {
        if (state is SettingsLoaded) {
          return _buildSettingsContent(state);
        }
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
  }

  Widget _buildSettingsContent(SettingsLoaded state) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color.fromARGB(150, 128, 103, 255),
            Color.fromARGB(200, 30, 20, 90)
          ],
        ),
        border: Border.all(color: Colors.white24, width: 1.2),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSettingsMenuItem(Icons.tune, 'preferences'.tr(), onTap: () {
              _showPreferencesScreen();
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.person_outline, 'profile'.tr(),
                onTap: () {
              context.pushNamed(AppRoutes.PROFILE_ROUTE_NAME);
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.star_outline, 'subscription'.tr(),
                onTap: () {
              context.pushNamed(AppRoutes.SUBSCRIPTION_ROUTE_NAME,
                  extra: false);
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.help_outline, 'help_and_support'.tr(),
                onTap: () {
              context.pushNamed(AppRoutes.HELP_SUPPORT_ROUTE_NAME,
                  extra: false);
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.privacy_tip, 'privacy_policy'.tr(),
                onTap: () {
              context.pushNamed(AppRoutes.TERMS_POLICIES_ROUTE_NAME);
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.description, 'terms_of_service'.tr(),
                onTap: () {
              context.pushNamed(AppRoutes.TERMS_POLICIES_ROUTE_NAME);
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.info_outline, 'about'.tr(), onTap: () {
              _showAboutDialog();
            }),
            const SizedBox(height: 12),
            _buildSettingsMenuItem(Icons.logout, 'logout'.tr(), onTap: () {
              _showLogoutDialog();
            }),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsMenuItem(IconData icon, String title,
      {VoidCallback? onTap}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: ListTile(
        leading: Icon(icon, color: Colors.white, size: 24),
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: Colors.white.withOpacity(0.7),
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }

  void _showPreferencesScreen() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return BlocProvider.value(
          value: _settingsBloc,
          child: Container(
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: const BoxDecoration(
              color: Color(0XFF221B67),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: BlocBuilder<SettingsBloc, SettingsState>(
              builder: (context, state) {
                if (state is SettingsLoaded) {
                  return _buildPreferencesContent(state);
                }
                return const Center(child: CircularProgressIndicator());
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildPreferencesContent(SettingsLoaded state) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'preferences'.tr(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close, color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildLanguageSelector(state),
                  const SizedBox(height: 16),
                  _buildThemeSelector(state),
                  const SizedBox(height: 16),
                  _buildNotificationsSection(state),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSelector(SettingsLoaded state) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: ListTile(
        leading: const Icon(Icons.language, color: Colors.white, size: 24),
        title: const Text(
          'Language',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          state.language.languageCode == 'en' ? 'English' : 'العربية',
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 14,
          ),
        ),
        trailing: DropdownButton<String>(
          value: state.language.languageCode,
          dropdownColor: Colors.grey[800],
          style: const TextStyle(color: Colors.white),
          underline: Container(),
          icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
          items: const [
            DropdownMenuItem(value: 'en', child: Text('English')),
            DropdownMenuItem(value: 'ar', child: Text('العربية')),
          ],
          onChanged: (String? newValue) {
            if (newValue != null) {
              context
                  .read<SettingsBloc>()
                  .add(ChangeLanguage(Locale(newValue)));
            }
          },
        ),
      ),
    );
  }

  Widget _buildThemeSelector(SettingsLoaded state) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: ListTile(
        leading: const Icon(Icons.brightness_6, color: Colors.white, size: 24),
        title: const Text(
          'Theme',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          _getThemeName(state.themeMode),
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 14,
          ),
        ),
        trailing: DropdownButton<ThemeMode>(
          value: state.themeMode,
          dropdownColor: Colors.grey[800],
          style: const TextStyle(color: Colors.white),
          underline: Container(),
          icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
          items: const [
            DropdownMenuItem(value: ThemeMode.system, child: Text('System')),
            DropdownMenuItem(value: ThemeMode.light, child: Text('Light')),
            DropdownMenuItem(value: ThemeMode.dark, child: Text('Dark')),
          ],
          onChanged: (ThemeMode? newValue) {
            if (newValue != null) {
              context.read<SettingsBloc>().add(ChangeTheme(newValue));
            }
          },
        ),
      ),
    );
  }

  String _getThemeName(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      default:
        return 'System';
    }
  }

  Widget _buildNotificationsSection(SettingsLoaded state) {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: ListTile(
            leading: const Icon(Icons.notifications_outlined,
                color: Colors.white, size: 24),
            title: const Text(
              'Push Notifications',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            trailing: Switch(
              value: state.pushNotificationsEnabled,
              onChanged: (bool value) {
                context
                    .read<SettingsBloc>()
                    .add(TogglePushNotifications(value));
              },
              activeColor: Colors.blue,
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: ListTile(
            leading:
                const Icon(Icons.email_outlined, color: Colors.white, size: 24),
            title: const Text(
              'Email Notifications',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            trailing: Switch(
              value: state.emailNotificationsEnabled,
              onChanged: (bool value) {
                context
                    .read<SettingsBloc>()
                    .add(ToggleEmailNotifications(value));
              },
              activeColor: Colors.blue,
            ),
          ),
        ),
      ],
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.grey[900],
          title: const Text(
            'Logout',
            style: TextStyle(color: Colors.white),
          ),
          content: const Text(
            'Are you sure you want to logout?',
            style: TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child:
                  const Text('Cancel', style: TextStyle(color: Colors.white)),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Logout functionality will be implemented'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
              child: const Text('Logout', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.grey[900],
          title: const Text(
            'About Saudi Sign Language',
            style: TextStyle(color: Colors.white),
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Center(
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      image: const DecorationImage(
                        image: AssetImage(APP_ICON),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                const Center(
                  child: Text(
                    'Saudi Sign Language',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Center(
                  child: Text(
                    'Version 1.0.0',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'About',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'The Saudi Sign Language app translates speech and text into sign language performed by an avatar. Our mission is to bridge communication gaps and make sign language accessible to everyone.',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }
}
