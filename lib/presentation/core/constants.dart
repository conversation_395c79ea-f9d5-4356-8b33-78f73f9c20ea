import 'package:arabic_sign_language/presentation/core/themes/text_theme.dart';
import 'package:flutter/material.dart';

//-----------  default character ----------------//
bool isDefaultCharacterSelected = true;

//--------------- Assets ---------------------//

const splashIcon = "assets/images/splash.png";
const notificationIcon = "assets/images/notification.png";
const String APP_ICON = 'assets/images/enhancement/app_icon.png';
const String APP_BG = 'assets/images/enhancement/app_bg.png';
const String IC_Google = 'assets/images/enhancement/google.png';
const String IC_Apple = 'assets/images/enhancement/apple.png';
const String IC_HOME = 'assets/images/enhancement/ic_home.png';
const String IC_TEXT = 'assets/images/enhancement/ic_text.png';
const String IC_SPEECH = 'assets/images/enhancement/ic_mic.png';
const String IC_VIDEO = 'assets/images/enhancement/ic_video.png';
const String IC_DICTIONARY = 'assets/images/enhancement/ic_dictionary.png';
// const String IC_MORE = 'assets/images/enhancement/ic_settings.png';
const String ARAB_AVATAR = 'assets/images/enhancement/arab_man.png';
const String TRANSLATE_TEXT = 'assets/images/enhancement/translate_text.png';
const String TRANSLATE_SPEECH =
    'assets/images/enhancement/translate_speech.png';
const String TRANSLATE_VIDEO = 'assets/images/enhancement/translate_video.png';
const String DICTIONARY = 'assets/images/enhancement/sign_dictionary.png';
const String SIGNTOTEXT = 'assets/images/enhancement/signto_text.png';
const String BACK_BUTTON = 'assets/images/enhancement/Play.png';
const String INFO = 'assets/images/enhancement/Info.png';
const String CAPTURE = 'assets/images/enhancement/capture.png';
const String LIVE = 'assets/images/enhancement/ic_live.png';
const String UPLOAD = 'assets/images/enhancement/ic_upload.png';
const String START_REC = 'assets/images/enhancement/ic_recorder.png';
const String STOP_REC = 'assets/images/enhancement/ic_recorder_stop.png';
const String IC_PROFILE = 'assets/images/enhancement/ic_profile.png';
const String IC_SETTINGS = 'assets/images/enhancement/ic_settings.png';
const String IC_ABOUT = 'assets/images/enhancement/ic_about.png';
const String IC_HELP = 'assets/images/enhancement/ic_help.png';
const String IC_LISTTILE = 'assets/images/enhancement/ic_listtile.png';
const String IC_MORE = 'assets/images/enhancement/ic_more.png';
const String IC_SIGNTOTEXT = 'assets/images/enhancement/ic_signtotext.png';
const String IC_SUBSCRIPTION = 'assets/images/enhancement/ic_subscription.png';
const String IC_TILEVIEW = 'assets/images/enhancement/ic_tileview.png';
const String IC_RECORD = 'assets/images/enhancement/ic_record_vid.png';
const String IC_TIPS = 'assets/images/enhancement/ic_tips.png';
const String IC_TRANSLATION = 'assets/images/enhancement/ic_translation.png';
const String IC_LOGOUT = 'assets/images/enhancement/ic_logout.png';

const String ACCESS_TOKEN = 'access_token';
const String ONBOARDING = 'onboarding';
const String LOGIN = 'Login';
const String CONTINUE_AS_GUEST = 'continue as guest';
const String OR_CONTINUE_WITH = 'Or Continue with';
const String FORGOT_PASSWORD = 'forgot password';
const String CONTINUE_WITH = 'Continue with';
const String DONT_HAVE_ACCOUNT = "Don't have an account?";
const String CREATE_NEW_ACCOUNT = 'Create New Account';
const String ENTER_EMAIL = 'Enter your Email Id';
const String ENTER_PASSWORD = 'Enter your password';

const double MARGIN = 18;
const double RADIUS = 8;
const double SPACE8 = 8;
const double SPACE4 = 4;
const double SPACE12 = 12;
const double SPACE15 = 15;
const double SPACE25 = 25;

const Color purpleMimosa = Color(0xFF9572FF);
const Color lightRoyalBlue = Color(0xFF3328EE);
const Color primaryWhite = Color(0xFFFFFFFF);

//---------Onboarding------------
const background = Color.fromARGB(255, 35, 35, 35);
// const background = Color.fromRGBO(52, 55, 80, 1);
const pageImageColor = Color.fromARGB(255, 212, 212, 212);
const pageTitleStyle = TextStyle(
  fontSize: 20.0,
  wordSpacing: 1,
  letterSpacing: 1.2,
  fontWeight: FontWeight.w900,
  color: Colors.black,
);
const pageInfoStyle = TextStyle(
  color: Colors.black,
  letterSpacing: 0.8,
  height: 1.6,
  fontSize: 14,
);

//---------Skip button-----------

const defaultSkipButtonColor = Color.fromARGB(255, 93, 93, 93);
const defaultSkipButtonBorderRadius = BorderRadius.all(Radius.circular(20.0));
const defaultSkipButtonPadding =
    EdgeInsets.symmetric(horizontal: 17.0, vertical: 5.0);
const defaultSkipButtonTextStyle =
    TextStyle(color: Colors.white, letterSpacing: 1.0);

//---------Signin button-----------

const signinButtonColor = Color.fromARGB(255, 158, 69, 69);
const signinButtonBorderRadius = BorderRadius.all(Radius.circular(20.0));
const signinButtonPadding =
    EdgeInsets.symmetric(horizontal: 17.0, vertical: 5.0);
const signinButtonTextStyle =
    TextStyle(color: Colors.white, letterSpacing: 1.0);

//--------Proceed Button---------

const defaultProceedButtonColor = Color.fromARGB(255, 88, 94, 147);
const defaultProceedButtonBorderRadius =
    BorderRadius.all(Radius.circular(20.0));
const defaultProceedButtonPadding =
    EdgeInsets.symmetric(horizontal: 17.0, vertical: 5.0);
const defaultProceedButtonTextStyle = TextStyle(
  color: Colors.white,
  letterSpacing: 1.0,
);

const appBg = Color(0xFF0068F8);
const buttonColor = Color(0xFF1E56B1);
const guestButtonColor = Color(0xFF289900);
final disabledButtonColor = Color(0xFF054DA4).withOpacity(0.4);
final overlayColor = Color(0xFF1D1E20).withOpacity(0.9);

//------------------ Fonts -------------------
const String FONT_FAMILY = 'Poppins';
const String FONT_FAMILY_INTER = 'Inter';

Widget onBoardingItem(
    {required String image,
    required String title,
    required String description}) {
  return Container(
    color: appBg,
    child: Column(
      children: [
        const SizedBox(height: 50),
        Text(
          "Welcome to",
          style: AppTextTheme.mediumBoldText(fontSize: 20),
        ),
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                  text: "Arabic ",
                  style: AppTextTheme.mediumBoldText(fontSize: 18)),
              TextSpan(text: "Sign Language", style: AppTextTheme.smallText())
            ],
          ),
        ),
        const SizedBox(height: 15),
        Image.asset(
          "assets/images/$image.png",
          height: 300,
        ),
        Expanded(
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(45),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Text(
                    title,
                    style: AppTextTheme.onBoardingText(
                        fontSize: 20,
                        fontColor: const Color(0xFF343434),
                        fontWeight: FontWeight.w600),
                    textAlign: TextAlign.left,
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                  child: Text(
                    description,
                    style: AppTextTheme.onBoardingText(
                        fontSize: 14,
                        fontColor: const Color(0xFF666666),
                        fontWeight: FontWeight.w400),
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    ),
  );
}

Widget imageItem(
    {required String image,
    double top = 0,
    double left = 0,
    double right = 0}) {
  return Positioned(
    top: top,
    left: left,
    right: right,
    child: Container(
      width: 120,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Color(0xFFC8C8C8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Image.asset(
          'assets/images/$image.png',
          width: 150,
          height: 160,
          // fit: BoxFit.fill,
        ),
      ),
    ),
  );
}

Widget onBoardingStackItem({required String title}) {
  return Container(
    color: appBg,
    child: Stack(
      fit: StackFit.passthrough,
      children: [
        Column(
          children: [
            const SizedBox(height: 50),
            Text(
              "Welcome to",
              style: AppTextTheme.mediumBoldText(fontSize: 20),
            ),
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                      text: "Arabic ",
                      style: AppTextTheme.mediumBoldText(fontSize: 18)),
                  TextSpan(
                      text: "Sign Language", style: AppTextTheme.smallText())
                ],
              ),
            ),
            const SizedBox(height: 15),
            Spacer(),
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(45),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      child: Text(
                        title,
                        style: AppTextTheme.onBoardingText(
                            fontSize: 20,
                            fontColor: const Color(0xFF666666),
                            fontWeight: FontWeight.w500),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
        imageItem(
          image: "arabman",
          top: 200,
          left: 220,
          right: 20,
        ),
        imageItem(
          image: "arabgirl",
          top: 313,
          left: 20,
          right: 220,
        ),
        imageItem(
          image: "arabgirl-2",
          top: 410,
          left: 220,
          right: 20,
        ),
      ],
    ),
  );
}

// -------------onboardingPagesList---------------
final onboardingPagesList = [
  onBoardingItem(
      image: "arabman",
      title: "About the app",
      description:
          "Globally awarded, the Hand Talk app assists in learning and understanding sign language through artificial intelligence. "),
  onBoardingStackItem(title: "You Can Select your favorite character "),
  onBoardingItem(
      image: "arabman",
      title: "Hello",
      description:
          "Hello, I’m Omar, Hand Talk’s virtual sign language translator and i’m here to help your!"),
  onBoardingItem(
      image: "arabman",
      title: "How it Works",
      description:
          "The app uses Artifical Intelligence to translate Arabic into Sign Language. Sometimes the translations may not be perfect."),
];

// ---------------- shared preference keys ----------------

const String kEYOnboardingCompleted = "isOnBoardingCompleted";
const String kEYShowCaseCompleted = 'isShowCaseCompleted';

const String kEYAccessToken = 'access_token';
const String kEYRefreshToken = 'refresh_token';
const String kEYRoleId = 'role_id';
const String kEYTokenType = 'token_type';

// Navigation bar persistence
const String kEYNavigationItems = 'navigation_items';
const String kEYCompletedOnboardingEmails = 'completed_onboarding_emails';

// Settings persistence keys
const String kEYThemeMode = 'themeMode';
const String kEYAvatarPersonality = 'avatarPersonality';
const String kEYPushNotifications = 'pushNotifications';
const String kEYEmailNotifications = 'emailNotifications';
const String kEYTextSize = 'textSize';
const String kEYContrast = 'contrast';
