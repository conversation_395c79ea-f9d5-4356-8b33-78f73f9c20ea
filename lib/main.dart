import 'dart:async';

import 'package:arabic_sign_language/bloc/Dictionary/dictionary_bloc.dart';
import 'package:arabic_sign_language/bloc/UnityScreen/unity_screen_bloc.dart';
import 'package:arabic_sign_language/bloc/VideoTranscription/video_transcription_bloc.dart';
import 'package:arabic_sign_language/bloc/auth/auth_bloc.dart';
import 'package:arabic_sign_language/bloc/connectionChecker/connection_checker_bloc.dart';
import 'package:arabic_sign_language/bloc/forgot_password/forgot_password_bloc.dart';
import 'package:arabic_sign_language/bloc/language/language_bloc.dart';
import 'package:arabic_sign_language/bloc/speechTranscript/speech_transcript_bloc.dart';
import 'package:arabic_sign_language/bloc/textTranscript/text_transcript_bloc.dart';
import 'package:arabic_sign_language/bloc/youtubeScreen/youtube_screen_bloc.dart';
import 'package:arabic_sign_language/data/service/general_usage_tracking.dart';
import 'package:arabic_sign_language/data/service/recoding_service.dart';
import 'package:arabic_sign_language/data/service/text_conversion_service.dart';
import 'package:arabic_sign_language/data/service/transcript_service.dart';
import 'package:arabic_sign_language/data/service/user_service.dart';
import 'package:arabic_sign_language/presentation/core/themes/theme.dart';
import 'package:arabic_sign_language/utilities/go_router_init.dart';
import 'package:arabic_sign_language/utilities/logger.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter/material.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'bloc/user/user_bloc.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

Future<void> main() async {
  logger.runLogging(
      () => runZonedGuarded(() async {
            WidgetsFlutterBinding.ensureInitialized();
            await WakelockPlus.enable();
            await EasyLocalization.ensureInitialized();
            await SystemChrome.setPreferredOrientations([
              DeviceOrientation.portraitUp,
            ]);
            if (Firebase.apps.isEmpty) {
              await Firebase.initializeApp();
            }
            runApp(EasyLocalization(
              supportedLocales: const [Locale('en'), Locale('ar')],
              path: 'assets/lang',
              fallbackLocale: const Locale('en'),
              child: MultiBlocProvider(
                providers: [
                  BlocProvider<UnityScreenBloc>(
                    create: (context) => UnityScreenBloc(
                      RecordingService(),
                      TextConversionService(),
                      TranscriptService(),
                    ),
                  ),
                  BlocProvider<YoutubeScreenBloc>(
                    create: (context) => YoutubeScreenBloc(
                      TranscriptService(),
                    ),
                  ),
                  BlocProvider(
                    create: (context) =>
                        ConnectionCheckerBloc()..add(ConnectivityObserver()),
                  ),
                  BlocProvider<LanguageBloc>(
                      create: (context) => LanguageBloc()),
                  // Add this to your existing BlocProvider list
                  BlocProvider<AuthBloc>(
                    create: (context) => AuthBloc(),
                  ),
                  BlocProvider<TextTranscriptBloc>(
                    create: (context) => TextTranscriptBloc(
                      TextConversionService(),
                    ),
                  ),
                  BlocProvider<SpeechTranscriptBloc>(
                    create: (context) => SpeechTranscriptBloc(
                      recordingService: RecordingService(),
                      textConversionService: TextConversionService(),
                    ),
                  ),
                  BlocProvider<VideoTranscriptionBloc>(
                    create: (context) => VideoTranscriptionBloc(
                      transcriptService: TranscriptService(),
                      recordingService: RecordingService(),
                    ),
                  ),
                  BlocProvider<DictionaryBloc>(
                    create: (context) => DictionaryBloc(
                      GeneralUsageTrackingService(),
                    ),
                  ),
                  BlocProvider<UserBloc>(
                    create: (context) => UserBloc(UserService()),
                  ),
                  BlocProvider<ForgotPasswordBloc>(
                    create: (ctx) => ForgotPasswordBloc(),
                  )
                ],
                child: const MyApp(),
              ),
            ));
          }, logger.logZoneError),
      const LogOptions());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      localizationsDelegates: context.localizationDelegates,
      supportedLocales: context.supportedLocales,
      locale: context.locale,
      debugShowCheckedModeBanner: false,
      title: 'ASL',
      themeMode: ThemeMode.dark,
      theme: themeLight(context),
      darkTheme: themeDark(context),
      routerConfig: routerInit,
      builder: (context, child) {
        final size = MediaQuery.sizeOf(context);
        return BlocListener<ConnectionCheckerBloc, ConnectionCheckerState>(
          listener: (context, state) {
            if (state is ConnectivityStatusChanged) {
              final isConnected = state.isNetworkConnected;
              final snackBar = SnackBar(
                content: Text(
                  isConnected
                      ? "Connected to the internet"
                      : "No internet connection",
                ),
                backgroundColor: isConnected ? Colors.green : Colors.red,
                behavior: SnackBarBehavior.floating,
                duration: Duration(
                  seconds: isConnected ? 3 : 1800,
                ),
                margin: EdgeInsets.only(
                  bottom: size.height * 0.05,
                  right: 10,
                  left: 10,
                ),
              );

              // Show the snackbar using ScaffoldMessenger
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
              ScaffoldMessenger.of(context).showSnackBar(snackBar);
            }
          },
          child: MediaQuery(
              data: MediaQuery.of(context)
                  .copyWith(textScaler: const TextScaler.linear(1.0)),
              child: child!),
        );
      },
    );
  }
}
