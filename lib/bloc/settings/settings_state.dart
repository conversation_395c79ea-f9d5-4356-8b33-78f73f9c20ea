import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

abstract class SettingsState extends Equatable {
  const SettingsState();

  @override
  List<Object?> get props => [];
}

class SettingsInitial extends SettingsState {}

class SettingsLoading extends SettingsState {}

class SettingsLoaded extends SettingsState {
  final Locale language;
  final ThemeMode themeMode;
  final String avatarPersonality;
  final bool pushNotificationsEnabled;
  final bool emailNotificationsEnabled;
  final double textSize;
  final double contrast;
  final String? userEmail;
  final String? userMobile;

  const SettingsLoaded({
    required this.language,
    required this.themeMode,
    required this.avatarPersonality,
    required this.pushNotificationsEnabled,
    required this.emailNotificationsEnabled,
    required this.textSize,
    required this.contrast,
    this.userEmail,
    this.userMobile,
  });

  @override
  List<Object?> get props => [
        language,
        themeMode,
        avatarPersonality,
        pushNotificationsEnabled,
        emailNotificationsEnabled,
        textSize,
        contrast,
        userEmail,
        userMobile,
      ];

  SettingsLoaded copyWith({
    Locale? language,
    ThemeMode? themeMode,
    String? avatarPersonality,
    bool? pushNotificationsEnabled,
    bool? emailNotificationsEnabled,
    double? textSize,
    double? contrast,
    String? userEmail,
    String? userMobile,
  }) {
    return SettingsLoaded(
      language: language ?? this.language,
      themeMode: themeMode ?? this.themeMode,
      avatarPersonality: avatarPersonality ?? this.avatarPersonality,
      pushNotificationsEnabled: pushNotificationsEnabled ?? this.pushNotificationsEnabled,
      emailNotificationsEnabled: emailNotificationsEnabled ?? this.emailNotificationsEnabled,
      textSize: textSize ?? this.textSize,
      contrast: contrast ?? this.contrast,
      userEmail: userEmail ?? this.userEmail,
      userMobile: userMobile ?? this.userMobile,
    );
  }
}

class SettingsError extends SettingsState {
  final String message;

  const SettingsError(this.message);

  @override
  List<Object?> get props => [message];
}

class SettingsSaving extends SettingsState {}

class SettingsSaved extends SettingsState {
  final String message;

  const SettingsSaved(this.message);

  @override
  List<Object?> get props => [message];
}

class EmailUpdateSuccess extends SettingsState {
  final String message;

  const EmailUpdateSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

class MobileUpdateSuccess extends SettingsState {
  final String message;

  const MobileUpdateSuccess(this.message);

  @override
  List<Object?> get props => [message];
}
