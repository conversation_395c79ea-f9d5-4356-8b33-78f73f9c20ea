import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

abstract class SettingsEvent extends Equatable {
  const SettingsEvent();

  @override
  List<Object?> get props => [];
}

// Language Events
class ChangeLanguage extends SettingsEvent {
  final Locale locale;
  
  const ChangeLanguage(this.locale);
  
  @override
  List<Object?> get props => [locale];
}

// Theme Events
class ChangeTheme extends SettingsEvent {
  final ThemeMode themeMode;
  
  const ChangeTheme(this.themeMode);
  
  @override
  List<Object?> get props => [themeMode];
}

// Avatar Personality Events
class ChangeAvatarPersonality extends SettingsEvent {
  final String avatarPersonality;
  
  const ChangeAvatarPersonality(this.avatarPersonality);
  
  @override
  List<Object?> get props => [avatarPersonality];
}

// Notification Events
class TogglePushNotifications extends SettingsEvent {
  final bool enabled;
  
  const TogglePushNotifications(this.enabled);
  
  @override
  List<Object?> get props => [enabled];
}

class ToggleEmailNotifications extends SettingsEvent {
  final bool enabled;
  
  const ToggleEmailNotifications(this.enabled);
  
  @override
  List<Object?> get props => [enabled];
}

// Accessibility Events
class ChangeTextSize extends SettingsEvent {
  final double textSize;
  
  const ChangeTextSize(this.textSize);
  
  @override
  List<Object?> get props => [textSize];
}

class ChangeContrast extends SettingsEvent {
  final double contrast;
  
  const ChangeContrast(this.contrast);
  
  @override
  List<Object?> get props => [contrast];
}

// Profile Events
class UpdateEmail extends SettingsEvent {
  final String email;
  
  const UpdateEmail(this.email);
  
  @override
  List<Object?> get props => [email];
}

class UpdateMobileNumber extends SettingsEvent {
  final String mobileNumber;
  
  const UpdateMobileNumber(this.mobileNumber);
  
  @override
  List<Object?> get props => [mobileNumber];
}

// Load Settings Event
class LoadSettings extends SettingsEvent {}

// Save Settings Event
class SaveSettings extends SettingsEvent {}
