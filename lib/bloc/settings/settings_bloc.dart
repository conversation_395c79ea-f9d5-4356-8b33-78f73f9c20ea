import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/service/user_service.dart';
import '../../presentation/core/constants.dart';
import 'settings_event.dart';
import 'settings_state.dart';

class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  final UserService _userService;

  SettingsBloc(this._userService) : super(SettingsInitial()) {
    on<LoadSettings>(_onLoadSettings);
    on<ChangeLanguage>(_onChangeLanguage);
    on<ChangeTheme>(_onChangeTheme);
    on<ChangeAvatarPersonality>(_onChangeAvatarPersonality);
    on<TogglePushNotifications>(_onTogglePushNotifications);
    on<ToggleEmailNotifications>(_onToggleEmailNotifications);
    on<ChangeTextSize>(_onChangeTextSize);
    on<ChangeContrast>(_onChangeContrast);
    on<UpdateEmail>(_onUpdateEmail);
    on<UpdateMobileNumber>(_onUpdateMobileNumber);
    on<SaveSettings>(_onSaveSettings);
  }

  Future<void> _onLoadSettings(
      LoadSettings event, Emitter<SettingsState> emit) async {
    emit(SettingsLoading());
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load user profile
      final user = await _userService.getUserProfile();

      // Load settings from SharedPreferences with defaults
      final languageCode = prefs.getString('languageCode') ?? 'en';
      final themeModeString = prefs.getString(kEYThemeMode) ?? 'system';
      final avatarPersonality =
          prefs.getString(kEYAvatarPersonality) ?? 'default';
      final pushNotifications = prefs.getBool(kEYPushNotifications) ?? true;
      final emailNotifications = prefs.getBool(kEYEmailNotifications) ?? true;
      final textSize = prefs.getDouble(kEYTextSize) ?? 1.0;
      final contrast = prefs.getDouble(kEYContrast) ?? 1.0;

      ThemeMode themeMode;
      switch (themeModeString) {
        case 'light':
          themeMode = ThemeMode.light;
          break;
        case 'dark':
          themeMode = ThemeMode.dark;
          break;
        default:
          themeMode = ThemeMode.system;
      }

      emit(SettingsLoaded(
        language: Locale(languageCode),
        themeMode: themeMode,
        avatarPersonality: avatarPersonality,
        pushNotificationsEnabled: pushNotifications,
        emailNotificationsEnabled: emailNotifications,
        textSize: textSize,
        contrast: contrast,
        userEmail: user?.email,
        userMobile: user?.mobile,
      ));
    } catch (e) {
      emit(SettingsError('Failed to load settings: $e'));
    }
  }

  Future<void> _onChangeLanguage(
      ChangeLanguage event, Emitter<SettingsState> emit) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final newState = currentState.copyWith(language: event.locale);
      emit(newState);

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('languageCode', event.locale.languageCode);
    }
  }

  Future<void> _onChangeTheme(
      ChangeTheme event, Emitter<SettingsState> emit) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final newState = currentState.copyWith(themeMode: event.themeMode);
      emit(newState);

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      String themeModeString;
      switch (event.themeMode) {
        case ThemeMode.light:
          themeModeString = 'light';
          break;
        case ThemeMode.dark:
          themeModeString = 'dark';
          break;
        default:
          themeModeString = 'system';
      }
      await prefs.setString(kEYThemeMode, themeModeString);
    }
  }

  Future<void> _onChangeAvatarPersonality(
      ChangeAvatarPersonality event, Emitter<SettingsState> emit) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final newState =
          currentState.copyWith(avatarPersonality: event.avatarPersonality);
      emit(newState);

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(kEYAvatarPersonality, event.avatarPersonality);
    }
  }

  Future<void> _onTogglePushNotifications(
      TogglePushNotifications event, Emitter<SettingsState> emit) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final newState =
          currentState.copyWith(pushNotificationsEnabled: event.enabled);
      emit(newState);

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(kEYPushNotifications, event.enabled);
    }
  }

  Future<void> _onToggleEmailNotifications(
      ToggleEmailNotifications event, Emitter<SettingsState> emit) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final newState =
          currentState.copyWith(emailNotificationsEnabled: event.enabled);
      emit(newState);

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(kEYEmailNotifications, event.enabled);
    }
  }

  Future<void> _onChangeTextSize(
      ChangeTextSize event, Emitter<SettingsState> emit) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final newState = currentState.copyWith(textSize: event.textSize);
      emit(newState);

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(kEYTextSize, event.textSize);
    }
  }

  Future<void> _onChangeContrast(
      ChangeContrast event, Emitter<SettingsState> emit) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final newState = currentState.copyWith(contrast: event.contrast);
      emit(newState);

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(kEYContrast, event.contrast);
    }
  }

  Future<void> _onUpdateEmail(
      UpdateEmail event, Emitter<SettingsState> emit) async {
    emit(SettingsSaving());
    try {
      // Here you would typically call an API to update the email
      // For now, we'll just update locally
      if (state is SettingsLoaded) {
        final currentState = state as SettingsLoaded;
        final newState = currentState.copyWith(userEmail: event.email);
        emit(newState);
        emit(const EmailUpdateSuccess('Email updated successfully'));
      }
    } catch (e) {
      emit(SettingsError('Failed to update email: $e'));
    }
  }

  Future<void> _onUpdateMobileNumber(
      UpdateMobileNumber event, Emitter<SettingsState> emit) async {
    emit(SettingsSaving());
    try {
      // Here you would typically call an API to update the mobile number
      // For now, we'll just update locally
      if (state is SettingsLoaded) {
        final currentState = state as SettingsLoaded;
        final newState = currentState.copyWith(userMobile: event.mobileNumber);
        emit(newState);
        emit(const MobileUpdateSuccess('Mobile number updated successfully'));
      }
    } catch (e) {
      emit(SettingsError('Failed to update mobile number: $e'));
    }
  }

  Future<void> _onSaveSettings(
      SaveSettings event, Emitter<SettingsState> emit) async {
    emit(SettingsSaving());
    try {
      // All settings are already saved individually, so this is just a confirmation
      emit(const SettingsSaved('Settings saved successfully'));
    } catch (e) {
      emit(SettingsError('Failed to save settings: $e'));
    }
  }
}
