import 'package:arabic_sign_language/data/service/auth_service.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/screens/login/login_screen.dart';
import 'package:arabic_sign_language/presentation/screens/signup/signup_email_screen.dart';
import 'package:arabic_sign_language/presentation/screens/signup/signup_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../common/router.dart';
import '../presentation/screens/account/help_support_screen.dart';
import '../presentation/screens/account/profile_screen.dart';
import '../presentation/screens/account/report_problem_screen.dart';
import '../presentation/screens/account/settings_screen.dart';
import '../presentation/screens/account/subscription_screen.dart';
import '../presentation/screens/account/terms_policies_screen.dart';
import '../presentation/screens/error/error_screen.dart';
import '../presentation/screens/forgot_password/change_password_screen.dart';
import '../presentation/screens/forgot_password/email_check_screen.dart';
import '../presentation/screens/forgot_password/forgot_password_screen.dart';
import '../presentation/screens/forgot_password/verification_screen.dart';
import '../presentation/screens/onboard/avatar_selection_screen.dart';
import '../presentation/screens/onboard/onboarding_screen.dart';
import '../presentation/screens/signup/signup_email_check_screen.dart';
import '../presentation/screens/splash/splash_screen.dart';
import 'logger.dart';

GoRouter routerInit = GoRouter(
  navigatorKey: AuthService.navigatorKey,
  routes: <RouteBase>[
    ///  =================================================================
    ///  ********************** Splash Route *****************************
    /// ==================================================================
    GoRoute(
      name: AppRoutes.SPLASH_ROUTE_NAME,
      path: AppRoutes.SPLASH_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const SplashScreen();
      },
    ),

    ///  =================================================================
    /// ********************** Authentication Routes ********************
    /// ==================================================================
    GoRoute(
      name: AppRoutes.LOGIN_ROUTE_NAME,
      path: AppRoutes.LOGIN_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const LoginScreen();
      },
    ),
    GoRoute(
      name: AppRoutes.SIGNUP_ROUTE_NAME,
      path: AppRoutes.SIGNUP_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        final email = state.extra as String;
        return SignupScreen(
          email: email,
        );
      },
    ),

    GoRoute(
      name: AppRoutes.SIGNUP_EMAIL_ROUTE_NAME,
      path: AppRoutes.SIGNUP_EMAIL_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const SignupEmailScreen();
      },
    ),

    GoRoute(
      name: AppRoutes.SIGN_UP_EMAIL_CHECK_ROUTE_NAME,
      path: AppRoutes.SIGN_UP_EMAIL_CHECK_ROUTE_PATH,
      builder: (context, state) {
        final email = state.extra as String;
        return SignupEmailCheckScreen(
          email: email,
        );
      },
    ),

    GoRoute(
      name: AppRoutes.FORGOT_PASSWORD_ROUTE_NAME,
      path: AppRoutes.FORGOT_PASSWORD_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const ForgotPasswordScreen();
      },
    ),

    GoRoute(
      name: AppRoutes.FORGOT_EMAIL_CHECK_ROUTE_NAME,
      path: AppRoutes.FORGOT_EMAIL_CHECK_ROUTE_PATH,
      builder: (context, state) {
        final email = state.extra as String;
        return EmailCheckScreen(
          email: email,
        );
      },
    ),

    GoRoute(
      name: AppRoutes.VERIFY_EMAIL_ROUTE_NAME,
      path: AppRoutes.VERIFY_EMAIL_ROUTE_PATH,
      builder: (context, state) {
        final email = state.extra as String;
        return VerificationScreen(
          email: email,
        );
      },
    ),

    GoRoute(
      name: AppRoutes.CHANGE_PASSWORD_ROUTE_NAME,
      path: AppRoutes.CHANGE_PASSWORD_ROUTE_PATH,
      builder: (context, state) {
        final data = state.extra as Map<String, String>;
        final email = data['email']!;
        final code = data['verificationCode']!;
        return ChangePasswordScreen(email: email, verificationCode: code);
      },
    ),

    ///  =================================================================
    /// ********************** DashBoard Route ******************************
    /// ==================================================================
    ///

    GoRoute(
      name: AppRoutes.ONBOARDING_ROUTE_NAME,
      path: AppRoutes.ONBOARDING_ROUTE_PATH,
      builder: (context, state) {
        final message = state.extra as String?;
        return OnboardingScreen(successMessage: message);
      },
    ),

    GoRoute(
      name: AppRoutes.AVATAR_SELECTION_ROUTE_NAME,
      path: AppRoutes.AVATAR_SELECTION_ROUTE_PATH,
      builder: (context, state) {
        return const AvatarSelectionScreen();
      },
    ),

    GoRoute(
      name: AppRoutes.DASHBOARD_ROUTE_NAME,
      path: AppRoutes.DASHBOARD_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const DashboardScreen();
      },
    ),
    GoRoute(
      name: AppRoutes.HOME_ROUTE_NAME,
      path: AppRoutes.HOME_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const HomeScreen();
      },
    ),

    ///  =================================================================
    /// ********************** Profile Route ******************************
    /// ==================================================================
    ///

    GoRoute(
      name: AppRoutes.PROFILE_ROUTE_NAME,
      path: AppRoutes.PROFILE_ROUTE_PATH,
      builder: (context, state) {
        return const ProfileScreen();
      },
    ),

    GoRoute(
      name: AppRoutes.SUBSCRIPTION_ROUTE_NAME,
      path: AppRoutes.SUBSCRIPTION_ROUTE_PATH,
      builder: (context, state) {
        // Get the flag.  If nothing was passed, default to false.
        final fromProfile = (state.extra as bool?) ?? false;

        return SubscriptionScreen(isFromProfile: fromProfile);
      },
    ),

    GoRoute(
      name: AppRoutes.HELP_SUPPORT_ROUTE_NAME,
      path: AppRoutes.HELP_SUPPORT_ROUTE_PATH,
      builder: (context, state) {
        // Get the flag.  If nothing was passed, default to false.
        final fromProfile = (state.extra as bool?) ?? false;

        return HelpSupportScreen(isFromProfile: fromProfile);
      },
    ),

    GoRoute(
      name: AppRoutes.TERMS_POLICIES_ROUTE_NAME,
      path: AppRoutes.TERMS_POLICIES_ROUTE_PATH,
      builder: (context, state) {
        return const TermsPoliciesScreen();
      },
    ),

    GoRoute(
      name: AppRoutes.REPORT_PROBLEM_ROUTE_NAME,
      path: AppRoutes.REPORT_PROBLEM_ROUTE_PATH,
      builder: (context, state) {
        return const ReportProblemScreen();
      },
    ),

    GoRoute(
      name: AppRoutes.SETTINGS_ROUTE_NAME,
      path: AppRoutes.SETTINGS_ROUTE_PATH,
      builder: (context, state) {
        return const SettingsScreen();
      },
    ),
  ],
  errorPageBuilder: (context, state) {
    return const MaterialPage(child: ErrorScreen());
  },
  redirect: (context, state) {
    logger.info('redirect: ${state.uri}');
    return null;
  },
);
