import 'package:arabic_sign_language/data/models/text_transcription_model/text_transcription_model.dart';
import 'package:arabic_sign_language/data/service/base_service.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../presentation/core/url.dart';

class TextConversionService extends BaseService {
  TextConversionService({Dio? dio})
      : super(
          baseUrl: AUTH_BASE_URL,
          headers: {"Content-Type": "application/json"},
        ) {
    if (dio != null) {
      this.dio = dio;
    }
    // Configure Dio to follow redirects and accept 307 status code
    this.dio.options.followRedirects = true;
    this.dio.options.maxRedirects = 5;
    this.dio.options.validateStatus = (status) {
      return status != null && status >= 200 && status < 500;
    };
  }

  Future<List<TextTranscriptionModel>> getTranscribedText(
      String text, String sourceType) async {
    List<TextTranscriptionModel> transcriptionList = [];
    try {
      final response = await dio.post(
        convertText,
        data: {
          "arabic_text": text,
          "source_type": sourceType, // Pass the source type here
        },
      );

      if (kDebugMode) {
        print("response => getTranscribedText => ${response.data}");
      }

      // Handle 307 redirect manually if needed
      if (response.statusCode == 307) {
        final redirectUrl = response.headers.value('location');
        if (redirectUrl != null) {
          if (kDebugMode) {
            print("Following redirect to: $redirectUrl");
          }

          final redirectResponse = await dio.post(
            redirectUrl,
            data: {
              "arabic_text": text,
            },
          );

          if (redirectResponse.statusCode == 200 &&
              redirectResponse.data['status']) {
            final List<dynamic> data = redirectResponse.data['data'];
            transcriptionList =
                data.map((i) => TextTranscriptionModel.fromJson(i)).toList();
          }
        }
      } else if (response.statusCode == 200 && response.data['status']) {
        final List<dynamic> data = response.data['data'];

        if (kDebugMode) {
          print("response => getTranscribedText => $data");
        }

        transcriptionList =
            data.map((i) => TextTranscriptionModel.fromJson(i)).toList();
      }
      return transcriptionList;
    } on DioException catch (e) {
      if (kDebugMode) {
        print("DioException in getTranscribedText: ${e.type}");
        print("DioException message: ${e.message}");
        print("DioException response: ${e.response?.statusCode}");
        print("DioException response data: ${e.response?.data}");

        if (e.response?.statusCode == 307) {
          print("Redirect location: ${e.response?.headers.value('location')}");
        }
      }

      // Try to handle redirect manually if it's a 307
      if (e.response?.statusCode == 307) {
        final redirectUrl = e.response?.headers.value('location');
        if (redirectUrl != null) {
          try {
            if (kDebugMode) {
              print("Manually following redirect to: $redirectUrl");
            }

            final redirectResponse = await dio.post(
              redirectUrl,
              data: {
                "arabic_text": text,
              },
            );

            if (redirectResponse.statusCode == 200 &&
                redirectResponse.data['status']) {
              final List<dynamic> data = redirectResponse.data['data'];
              transcriptionList =
                  data.map((i) => TextTranscriptionModel.fromJson(i)).toList();
              return transcriptionList;
            }
          } catch (redirectError) {
            if (kDebugMode) {
              print("Error following redirect: $redirectError");
            }
          }
        }
      }

      return transcriptionList;
    } catch (e) {
      if (kDebugMode) {
        print("error => getTranscribedText => $e");
      }
      return transcriptionList;
    }
  }

  Future<String> startUsageTrack(Map<String, dynamic> data) async {
    try {
      final response = await dio.post(
        START_USAGE_TRACK,
        data: data,
      );

      if (kDebugMode) {
        print("response => startUsageTrack => ${response.data}");
      }

      if (response.statusCode == 200 && response.data['status']) {
        return response.data['tracking_id'];
      } else {
        return "Failed to start usage track.";
      }
    } on DioException catch (e) {
      if (kDebugMode) {
        print("DioException response data: ${e.response?.data}");
      }
      return "Error occurred while starting usage track.";
    } catch (e) {
      if (kDebugMode) {
        print("error => startUsageTrack => $e");
      }
      return "Error occurred while starting usage track.";
    }
  }

  Future<void> updateUsageTrack(Map<String, dynamic> data) async {
    try {
      await dio.post(
        UPDATE_USAGE_TRACK,
        data: data,
      );
    } on DioException catch (e) {
      if (kDebugMode) {
        print("DioException response data: ${e.response?.data}");
      }
    } catch (e) {
      if (kDebugMode) {
        print("error => updateUsageTrack => $e");
      }
    }
  }
}
