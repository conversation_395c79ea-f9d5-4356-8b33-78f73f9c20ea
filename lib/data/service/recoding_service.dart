import 'dart:convert';
import 'dart:io';

import 'package:arabic_sign_language/data/models/audio/audio_response.dart';
import 'package:arabic_sign_language/data/models/recording_model/recording_model.dart';
import 'package:arabic_sign_language/data/service/base_service.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../presentation/core/url.dart';
import '../models/audio/upload_chunk_response.dart';
import '../models/audio/upload_session_response.dart';

class RecordingService extends BaseService {
  RecordingService()
      : super(
          baseUrl: AUTH_BASE_URL,
          headers: {"Content-Type": "multipart/form-data"},
        );
  int retryCount = 0;
  Future<List<RecordingModel>> getTranscriptionFromRecording(
      Uint8List recording, String sourceType,
      {String? fileName, ValueNotifier<double>? progressNotifier}) async {
    List<RecordingModel> recordingData = [];
    try {
      final formData = FormData.fromMap({
        'file': MultipartFile.fromBytes(
          recording,
          filename: fileName ?? 'recording.m4a',
        ),
        "source_type": sourceType,
      });

      // Increase timeout for file upload transcription (2 minutes)
      final response = await dio.post(
        uploadVoice,
        data: formData,
        options: Options(
          receiveTimeout:
              const Duration(minutes: 2), // 2 minutes for transcription
          sendTimeout: const Duration(minutes: 1), // 1 minute for upload
        ),
        onSendProgress: (count, total) {
          double progress = count / total;
          progressNotifier?.value = progress;
        },
      );

      print("response => getTranscriptionFromRecording => ${response.data}");
      if (response.statusCode == 200 && response.data['status']) {
        final List<dynamic> data = response.data['data'];
        print("recordings => getTranscriptionFromRecording => $data");
        recordingData = data.map((e) => RecordingModel.fromJson(e)).toList();
        print("recordings => getTranscriptionFromRecording => $recordingData");
      }
      return recordingData;
    } catch (e) {
      print("error => getTranscriptionFromRecording =>$e");
      return recordingData;
    }
  }

  Future<UploadSessionResponse?> createUploadSession(
      String filename, String sourceType) async {
    try {
      var data = json.encode({"filename": filename, "source_type": sourceType});
      final response = await dio.post(
        createSession,
        data: data,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
          receiveTimeout:
              const Duration(minutes: 2), // 2 minutes for transcription
          sendTimeout: const Duration(minutes: 1), // 1 minute for upload
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return UploadSessionResponse.fromJson(response.data);
      } else {
        return null;
      }
    } on DioException catch (e) {
      return null;
    } catch (e, stack) {
      debugPrintStack(stackTrace: stack);
      return null;
    }
  }

  Future<bool> startAudioChunkTranscription(
      int audioSessionId, String sourceType) async {
    try {
      final response = await dio.post(
        audioChunkTranscription,
        data: {"session_id": audioSessionId, "source_type": sourceType},
        options: Options(
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          receiveTimeout: const Duration(minutes: 2),
          sendTimeout: const Duration(minutes: 1),
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['status'] == true &&
            data['message']
                    ?.toString()
                    .contains("Audio transcription started") ==
                true) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } on DioException catch (e) {
      if (e.response != null) {}
      return false;
    } catch (e, stack) {
      debugPrintStack(stackTrace: stack);
      return false;
    }
  }

  Future<AudioResponse?> getAudioTranscription(
      int audioSessionId, int startTime) async {
    try {
      final response = await dio.post(
        getAudioTranscriptionStatus,
        data: {
          "session_id": audioSessionId,
          "start_time": startTime,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
          receiveTimeout: const Duration(minutes: 2),
          sendTimeout: const Duration(minutes: 1),
        ),
      );
      if (response.statusCode == 200) {
        if (response.data['status'] == "Audio is queued for processing") {
          await Future.delayed(const Duration(seconds: 5));
          retryCount = retryCount + 1;
          if (retryCount >= 4) {
            return null;
          }
          return getAudioTranscription(audioSessionId, startTime);
        } else if (response.data['status'] == "Audio is being processed") {
          await Future.delayed(const Duration(seconds: 5));
          retryCount = retryCount + 1;
          if (retryCount >= 4) {
            return null;
          }
          return getAudioTranscription(audioSessionId, startTime);
        } else if (response.data['status'] != false) {
          final audioResponse = AudioResponse.fromJson(response.data);

          if (audioResponse.status == false) {
            if (audioResponse.detail != null) {
              // Server returned a specific error
              debugPrint("Error: ${audioResponse.detail}");
              return null;
            } else {
              debugPrint("Unknown server error");
            }
          } else if (audioResponse.message ==
              "Audio is queued for processing") {
            debugPrint("Audio is still queued. Please wait...");
            // maybe retry later or poll with a delay
          } else if (audioResponse.data.isNotEmpty) {
            debugPrint("Received audio processing data: ${audioResponse.data}");
            // handle final result
            return audioResponse;
          } else {
            debugPrint("No data received yet, continue polling...");
          }
        }
      }
    } on DioException catch (e) {
      if (e.response != null) {}
      return null;
    } catch (e, stack) {
      debugPrintStack(stackTrace: stack);
      return null;
    }
    return null;
  }

  Future<UploadChunkResponse?> uploadAudioChunk({
    required File audioFile,
    required int sessionId,
    required int chunkNumber,
    required int totalChunks,
  }) async {
    try {
      final formData = FormData.fromMap({
        'chunk_data': [
          await MultipartFile.fromFile(
            audioFile.path,
            filename: audioFile.path.split('/').last,
          )
        ],
        'chunk_number': chunkNumber.toString(),
        'total_chunks': totalChunks.toString(),
        'session_id': sessionId.toString(),
      });

      final response = await dio.post(
        uploadChunk,
        data: formData,
        options: Options(
          contentType: 'multipart/form-data',
          sendTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 60),
        ),
      );

      if (response.statusCode == 200) {
        return UploadChunkResponse.fromJson(response.data);
      } else {
        return null;
      }
    } on DioException catch (e) {
      if (e.response != null) {}
      return null;
    } catch (e, stack) {
      debugPrintStack(stackTrace: stack);
      return null;
    }
  }
}
