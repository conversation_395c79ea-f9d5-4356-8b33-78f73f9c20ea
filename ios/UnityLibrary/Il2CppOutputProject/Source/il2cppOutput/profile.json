{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {"os": "Unix 15.4.0", "cpuCount": "8", "mappedPhysicalMemory": "40mb", "commandLine": "\"/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/build/deploy_arm64/il2cpp.dll\" --compile-cpp --platform=iOS \"--baselib-directory=/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Libraries\" --additional-defines=IL2CPP_DEBUG=0 --incremental-g-c-time-slice=3 --dotnetprofile=unityaot-macos --profiler-report --print-command-line \"--external-lib-il2-cpp=/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Libraries/libil2cpp.a\" --generatedcppdir=Il2CppOutputProject/Source/il2cppOutput --architecture=arm64 \"--outputpath=/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/build/ios/Debug-iphoneos/libGameAssembly.a\" --cachedirectory=/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbfggqwtkgawgeadtkjuokuewdvk/Build/Intermediates.noindex/Unity-iPhone.build/Debug-iphoneos/artifacts/arm64 --configuration=Debug"}, "traceEvents": [{"pid": 4350, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "il2cpp_outer"}}, {"pid": 4350, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 4350, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen2"}}, {"pid": 4350, "tid": 1, "ts": 1756725240235239, "dur": 1244, "ph": "X", "name": "GC - Gen2", "args": {}}, {"pid": 4350, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen1"}}, {"pid": 4350, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen0"}}, {"pid": 4350, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": "Main thread"}}, {"pid": 4350, "tid": 1, "ts": 1756725239814007, "dur": 393820, "ph": "X", "name": "il2cpp.exe", "args": {"analytics": "1"}}, {"pid": 4350, "tid": 1, "ts": 1756725239815257, "dur": 47644, "ph": "X", "name": "ParseArguments", "args": {}}, {"pid": 4350, "tid": 1, "ts": 1756725239862903, "dur": 10264, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {}}, {"pid": 4350, "tid": 1, "ts": 1756725239885145, "dur": 307114, "ph": "X", "name": "BeeDriverRunner.ExecuteIl2Cpp", "args": {}}, {"pid": 4350, "tid": 1, "ts": 1756725240195445, "dur": 10753, "ph": "X", "name": "Write Analytics", "args": {}}, {"pid": 4350, "tid": 1, "ts": 1756725240207829, "dur": 14277, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {}}, {"pid": 4350, "tid": 1, "ts": 1756725240228960, "dur": 1391, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 4350, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 4350, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 4350, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": "Main thread"}}, {"pid": 4350, "tid": 1, "ts": 1756725239988453, "dur": 206287, "ph": "X", "name": "Build FinalProgram", "args": {}}, {"pid": 4350, "tid": 1, "ts": 1756725239989795, "dur": 8875, "ph": "X", "name": "Writing build program input data", "args": {}}, {"pid": 4350, "tid": 1, "ts": 1756725239998686, "dur": 195990, "ph": "X", "name": "Running build system backend", "args": {}}, {"pid": 4350, "tid": 1, "ts": 1756725240194699, "dur": 40, "ph": "X", "name": "Finishing", "args": {}}, {"pid": 4350, "tid": 1, "ts": 1756725240203145, "dur": 1190, "ph": "X", "name": "Main thread", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756725240066302, "dur": 6771, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756725240073199, "dur": 12046, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756725240085265, "dur": 177, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756725240085447, "dur": 8758, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756725240094205, "dur": 74236, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756725240168564, "dur": 4096, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756725240085557, "dur": 8822, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240094379, "dur": 46795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240141185, "dur": 227, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.XRModule_CodeGen.c"}}, {"pid": 12345, "tid": 1, "ts": 1756725240141412, "dur": 538, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.XRModule_CodeGen.c"}}, {"pid": 12345, "tid": 1, "ts": 1756725240142014, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240141997, "dur": 132, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240141184, "dur": 945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/39spirhah7t1.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240142129, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240142581, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c"}}, {"pid": 12345, "tid": 1, "ts": 1756725240142728, "dur": 50, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240142580, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/yfn7vq7n2ssz.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240142779, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240142859, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule_CodeGen.c"}}, {"pid": 12345, "tid": 1, "ts": 1756725240142935, "dur": 117, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule_CodeGen.c"}}, {"pid": 12345, "tid": 1, "ts": 1756725240143063, "dur": 67, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240142857, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/wc6xn0rnm3rx.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240143131, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240143265, "dur": 52, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240143219, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/55p7gi2n894a.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240143317, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240143409, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xht2hp52y26b.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240143471, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240143657, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/s6fd6wynm3kn.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240143788, "dur": 50, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240143745, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/5k8kjjjsx9u6.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240143905, "dur": 231, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240143857, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ld0en8g94llk.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240144137, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240144268, "dur": 5339, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240149611, "dur": 193, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240144264, "dur": 5540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/tpeqsxory6ti.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240149804, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240149986, "dur": 115, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__8.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240150104, "dur": 163, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240149960, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ifpgdx7329kw.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240150267, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240150430, "dur": 396, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240150885, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240151102, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240150836, "dur": 726, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240150380, "dur": 1182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/qwuzhpkdq74u.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240151563, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240151695, "dur": 652, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240152357, "dur": 340, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240151682, "dur": 1016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/8napw69po3ot.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240152698, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240152867, "dur": 133, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240153146, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/dense_hash_map.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240153004, "dur": 404, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240152843, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/x43r6gw37lxs.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240153408, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240153627, "dur": 96, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240153813, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Thread.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240153729, "dur": 378, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240153603, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/k8hblmxu740r.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240154108, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240154311, "dur": 684, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240155087, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config-api.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240155005, "dur": 552, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240154268, "dur": 1289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/9okv497w31pj.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240155588, "dur": 209, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240155801, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240155570, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/y30xm8bce8b5.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240155935, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240156158, "dur": 220, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240156462, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/VmThreadUtils.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240156383, "dur": 599, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240156146, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/saat1sglwppb.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240156983, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240157141, "dur": 135, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__10.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240157281, "dur": 234, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240157123, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/srpmh3ozgc65.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240157516, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240157683, "dur": 141, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240158089, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Compiler/CompilerEnvironmentClang.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240158189, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericInstCompare.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240158336, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/KeyWrapper.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240157830, "dur": 669, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240157662, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7dlpmug0gh06.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240158499, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240158618, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Numerics.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240158718, "dur": 611, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Numerics.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240159487, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm/PlatformInvoke.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240160170, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/VmStringUtils.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240160226, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorCode.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240159336, "dur": 944, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240158617, "dur": 1663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/5cl2vu2ddnxj.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240160281, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240160566, "dur": 978, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__5.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240162029, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/ErrorCodes.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240161556, "dur": 729, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240160522, "dur": 1764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kghbtxhuebul.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240162286, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240162429, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__18.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240162488, "dur": 710, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__18.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240163510, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/hashtable-common.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240163211, "dur": 560, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240162428, "dur": 1343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/argcntcxgyhw.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240163771, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240163938, "dur": 422, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240164380, "dur": 61, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 1, "ts": 1756725240164500, "dur": 355, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240163928, "dur": 927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/b5u2bngduhxc.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240164855, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240165016, "dur": 131, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCTypeValuesTable.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240165153, "dur": 399, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240164987, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ykjf64ve6s6b.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240165552, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240165628, "dur": 99, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240165731, "dur": 376, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240165603, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/y15u6e2j6cs2.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240166108, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240166256, "dur": 236, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756725240166534, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SystemFutex.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240166764, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextCompare.h"}}, {"pid": 12345, "tid": 1, "ts": 1756725240166498, "dur": 668, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756725240166222, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/v7k6w0x73t9m.o"}}, {"pid": 12345, "tid": 1, "ts": 1756725240167210, "dur": 1229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240085549, "dur": 8663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240094213, "dur": 46917, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240141132, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText zmq4/_dummy_for_header_discovery"}}, {"pid": 12345, "tid": 2, "ts": 1756725240141433, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240141612, "dur": 8181, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240150045, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 2, "ts": 1756725240150145, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericMethodCompare.h"}}, {"pid": 12345, "tid": 2, "ts": 1756725240149802, "dur": 702, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240141593, "dur": 8911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ispzi233l2l5.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240150505, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240150585, "dur": 89, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__12.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240150679, "dur": 164, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240150570, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dv8wuz725tpv.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240150890, "dur": 94, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__11.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240150988, "dur": 209, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240150875, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/sf58eqt525dc.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240151197, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240151396, "dur": 199, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240151599, "dur": 295, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240151363, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kxd80fwss097.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240151959, "dur": 122, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240152085, "dur": 168, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240151921, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/wmbaxi9di32s.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240152253, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240152553, "dur": 451, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240153014, "dur": 386, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240152533, "dur": 867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/oiz5l1ysv3hf.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240153401, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240153580, "dur": 321, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240153906, "dur": 257, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240153554, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/3l87mr34ulq7.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240154163, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240154325, "dur": 305, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__5.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240154641, "dur": 283, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240154289, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/5f48dngj2rim.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240154924, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240155060, "dur": 116, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__9.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240155375, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/ArchitectureDetection.h"}}, {"pid": 12345, "tid": 2, "ts": 1756725240155181, "dur": 300, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240155042, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/rufv2vf1uc9l.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240155481, "dur": 591, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240156077, "dur": 479, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__10.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240156562, "dur": 340, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240156072, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/vxsvs7fdur9t.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240156903, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240157080, "dur": 175, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__2.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240157260, "dur": 224, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240157068, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/30why8aizhin.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240157485, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240157652, "dur": 164, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__4.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240158097, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericMethodCompare.h"}}, {"pid": 12345, "tid": 2, "ts": 1756725240157822, "dur": 566, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240157628, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/stod23uxj5bq.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240158389, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240158597, "dur": 429, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240159057, "dur": 323, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240158550, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/pnchmmpn7x4p.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240159421, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__2.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240159559, "dur": 104, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__2.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240159668, "dur": 284, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240159419, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/jkq99ycasc9u.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240159953, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240160218, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__7.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240160277, "dur": 954, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__7.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240162062, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_WakeupFallbackStrategy.h"}}, {"pid": 12345, "tid": 2, "ts": 1756725240161301, "dur": 919, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240160217, "dur": 2003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/634ftmcql1mk.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240162221, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240162405, "dur": 664, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__19.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240163509, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_Lock_FutexBased.inl.h"}}, {"pid": 12345, "tid": 2, "ts": 1756725240163728, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericInstCompare.h"}}, {"pid": 12345, "tid": 2, "ts": 1756725240163105, "dur": 756, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240162380, "dur": 1481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/bz0s78svk0yb.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240163862, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240164090, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Mono.Security__1.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240164145, "dur": 395, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Mono.Security__1.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240164546, "dur": 317, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240164089, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/cx1eyqer81ig.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240164926, "dur": 215, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppInteropDataTable.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240165389, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Win32/ThreadLocalValueImpl.h"}}, {"pid": 12345, "tid": 2, "ts": 1756725240165146, "dur": 456, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240164902, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/pf317lrw3uwk.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240165602, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240165814, "dur": 106, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240165926, "dur": 361, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240165785, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dbo5l25id83i.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240166333, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__1.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240166390, "dur": 246, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__1.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756725240166688, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 2, "ts": 1756725240166767, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringUtils.h"}}, {"pid": 12345, "tid": 2, "ts": 1756725240166646, "dur": 499, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240166332, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/9jx1wb3bx3go.o"}}, {"pid": 12345, "tid": 2, "ts": 1756725240167146, "dur": 902, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756725240168051, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240085552, "dur": 8820, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240094373, "dur": 46759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240141139, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText zmq4/libGameAssembly.objectfiles"}}, {"pid": 12345, "tid": 3, "ts": 1756725240141434, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240141584, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756725240141635, "dur": 298, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756725240142001, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 3, "ts": 1756725240141988, "dur": 159, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240141582, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/0y21cnyhbavb.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240142147, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240142573, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubsystemsModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756725240142669, "dur": 100, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubsystemsModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756725240142780, "dur": 131, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240142572, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/18qoqjfp2344.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240142945, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756725240142995, "dur": 55, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756725240142944, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kob167w4iqs3.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240143074, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240143201, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/nn1uwza8srva.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240143313, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/lh4lyk90achf.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240143374, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240143614, "dur": 116, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240143523, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/msowb4nh0fl7.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240143730, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240143928, "dur": 5877, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/__Generated.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240149810, "dur": 207, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240143913, "dur": 6104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/3rlsr07jdq2v.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240150065, "dur": 85, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__7.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240150154, "dur": 229, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240150038, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xzlsxnt6zl9c.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240150384, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240150552, "dur": 250, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__13.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240150882, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Handle.h"}}, {"pid": 12345, "tid": 3, "ts": 1756725240151102, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 3, "ts": 1756725240150812, "dur": 829, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240150536, "dur": 1105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/e3sdffo19v6a.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240151650, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240151779, "dur": 526, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__1.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240152317, "dur": 323, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240151758, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/funyd0vy2ndu.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240152694, "dur": 164, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240152903, "dur": 310, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240152667, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/y0z7393wloop.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240153213, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240153395, "dur": 122, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240153611, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config-api.h"}}, {"pid": 12345, "tid": 3, "ts": 1756725240153813, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Exception.h"}}, {"pid": 12345, "tid": 3, "ts": 1756725240153521, "dur": 573, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240153373, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/461ot2bidqkq.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240154096, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240154280, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/uqy3voe6yaey.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240154390, "dur": 3569, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240157998, "dur": 203, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__2.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240158206, "dur": 357, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240157959, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/myh16k22dton.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240158564, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240158796, "dur": 299, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Configuration.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240159119, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_ReentrantLock.inl.h"}}, {"pid": 12345, "tid": 3, "ts": 1756725240159240, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config-api.h"}}, {"pid": 12345, "tid": 3, "ts": 1756725240159476, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/HashUtils.h"}}, {"pid": 12345, "tid": 3, "ts": 1756725240159099, "dur": 680, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240158759, "dur": 1020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/hk5kkq7jonka.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240159779, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240159981, "dur": 1020, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__9.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240161514, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Debugger.h"}}, {"pid": 12345, "tid": 3, "ts": 1756725240161011, "dur": 738, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240159950, "dur": 1799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/czbso54krdan.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240161749, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240161892, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__20.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240161953, "dur": 358, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__20.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240162840, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Il2CppError.h"}}, {"pid": 12345, "tid": 3, "ts": 1756725240162316, "dur": 862, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240161891, "dur": 1287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xrrx40rwtyko.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240163179, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240163331, "dur": 337, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__15.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240163698, "dur": 218, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240163310, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/3npj7u336jde.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240163916, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240164064, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240164125, "dur": 424, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240164560, "dur": 462, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240164063, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/b916rvhibrfz.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240165023, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240165272, "dur": 519, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCFieldValuesTable.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240166059, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen.h"}}, {"pid": 12345, "tid": 3, "ts": 1756725240165818, "dur": 382, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240165241, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/o2aouj7qt9vq.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240166265, "dur": 246, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__2.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756725240166518, "dur": 261, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240166238, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/uz0pclh9zrs7.o"}}, {"pid": 12345, "tid": 3, "ts": 1756725240166780, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756725240166952, "dur": 1508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240085566, "dur": 8819, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240094386, "dur": 46810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240141220, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1756725240141413, "dur": 550, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1756725240141982, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240141967, "dur": 182, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240141198, "dur": 951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ltgue0fsouv2.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240142149, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240142536, "dur": 95, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1756725240143001, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240142819, "dur": 300, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240142532, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/i30949sfyigv.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240143172, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240143304, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ilrc8krxz2lu.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240143367, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240143487, "dur": 93, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240143460, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/n30c0tr66gr5.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240143582, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240143795, "dur": 70, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240143745, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/6rud99vuiqta.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240143883, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/eq6bhhidvzcc.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240144120, "dur": 5534, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__2.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240149969, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Debugger.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240150046, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/hashtable-common.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240149664, "dur": 687, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240144107, "dur": 6244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/jind86zpt72d.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240150405, "dur": 569, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__5.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240151101, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericInstHash.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240151460, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240150979, "dur": 614, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240150371, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/bcynxyyatvge.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240151593, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240151776, "dur": 121, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__2.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240151901, "dur": 276, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240151752, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/09qmt99v5bpd.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240152177, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240152328, "dur": 465, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240152799, "dur": 258, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240152314, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xljoc6rux3e4.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240153058, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240153165, "dur": 131, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule__1.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240153611, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_Lock_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240153819, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Debugger.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240153301, "dur": 818, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240153138, "dur": 981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/f4ttkhe820ko.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240154162, "dur": 291, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240154464, "dur": 423, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240154145, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/scqzmfkaf599.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240154888, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240155103, "dur": 82, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__8.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240155373, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericMethodCompare.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240155189, "dur": 315, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240155081, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/6ec7mm13rox2.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240155504, "dur": 717, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240156236, "dur": 167, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__9.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240156492, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Exception.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240156409, "dur": 252, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240156221, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/2mir9e71ly26.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240156661, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240156812, "dur": 380, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__5.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240157671, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Alignment.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240157204, "dur": 625, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240156789, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/a4nae7jb9vm5.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240157829, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240158121, "dur": 101, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240158325, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppSignature.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240158675, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/template_util.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240158226, "dur": 622, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240158082, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/i2hc1muj0ktj.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240158849, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240159044, "dur": 187, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__4.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240159476, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_StaticAssert.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240159263, "dur": 495, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240159017, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/57deao75y21t.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240159759, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240159956, "dur": 1047, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240161010, "dur": 598, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240159949, "dur": 1659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/t8gi1ceuhem8.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240161608, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240161836, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__22.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240161898, "dur": 442, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__22.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240162832, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Thread.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240162347, "dur": 1008, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240161835, "dur": 1520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/3my9zjkrza8q.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240163356, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240163453, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__13.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240163557, "dur": 288, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__13.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240164390, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/GenericMethod.h"}}, {"pid": 12345, "tid": 4, "ts": 1756725240163849, "dur": 813, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240163453, "dur": 1210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/h01e7587ztkf.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240164727, "dur": 300, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppReversePInvokeWrapperTable.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240165035, "dur": 254, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240164690, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/f8c7sw7hc9jd.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240165358, "dur": 300, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues1.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240165662, "dur": 468, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240165333, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/2j9wnqe5s33o.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240166131, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240166438, "dur": 167, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__1.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756725240166610, "dur": 507, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240166422, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/u7ac3yv4h71v.o"}}, {"pid": 12345, "tid": 4, "ts": 1756725240167121, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756725240167328, "dur": 1095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240085552, "dur": 8816, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240094368, "dur": 46784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240141161, "dur": 802, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1756725240141982, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240141967, "dur": 162, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240141153, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ni62wnjjywqn.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240142129, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240142543, "dur": 146, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240142516, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/cgmbvz4k17kf.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240142727, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/abt68c5vezfz.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240142847, "dur": 290, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1756725240142818, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7nahgss95by3.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240143163, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240143217, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/sfvvjoqxbb29.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240143284, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240143498, "dur": 139, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240143463, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7ud7tqeckn3w.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240143638, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240143811, "dur": 75, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240143761, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/t1lbx9mqbuee.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240143887, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240144043, "dur": 5573, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VRModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240149965, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Alignment.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240150060, "dur": 238, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorCode.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240149620, "dur": 678, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240143999, "dur": 6299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xaf043wt1g6r.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240150298, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240150480, "dur": 453, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240151099, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/dynamic_array.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240150937, "dur": 319, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240150442, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/486uh0phjhcw.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240151257, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240151387, "dur": 154, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240151780, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/ReentrantLock.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240152032, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Baselib.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240151666, "dur": 501, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240151370, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dizc0yequ1zd.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240152167, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240152412, "dur": 191, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240152689, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ReentrantLock.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240152854, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_CappedSemaphore.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240153145, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/hashtable-common.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240152612, "dur": 835, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240152396, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ii1nxgsvxhig.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240153472, "dur": 200, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240153816, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/WaitStatus-c-api.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240153677, "dur": 532, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240153456, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/nf366svp41cr.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240154209, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240154378, "dur": 105, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__3.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240154488, "dur": 190, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240154361, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/jsg4dylnv1pn.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240154678, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240154908, "dur": 91, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240155098, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Atomic.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240155005, "dur": 405, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240154884, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ucnctn1ynn63.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240155410, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240155553, "dur": 116, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__5.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240155673, "dur": 151, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240155543, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/zble7nsaiatk.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240155824, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240156053, "dur": 119, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240156177, "dur": 414, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240156048, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/s8tlrpjh8vo2.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240156592, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240156822, "dur": 181, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__7.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240157017, "dur": 367, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240156783, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/wrbfewe7fmf4.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240157385, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240157535, "dur": 234, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__1.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240157777, "dur": 368, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240157511, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/2sc1dtujvf17.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240158145, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240158350, "dur": 114, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240158681, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/ReentrantLock.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240158469, "dur": 502, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240158308, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/mvkh8vqeh0sp.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240158971, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240159258, "dur": 522, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__3.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240159847, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/dense_hash_map.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240160161, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Event.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240160227, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/sparseconfig.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240159784, "dur": 882, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240159228, "dur": 1438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ksaquet8veqa.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240160666, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240160868, "dur": 472, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__4.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240161797, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorCode.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240161394, "dur": 487, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240160841, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/z0dndo3wbni2.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240161881, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240162023, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240162161, "dur": 423, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240162831, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240162612, "dur": 783, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240162022, "dur": 1373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ddqidz804wp3.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240163396, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240163583, "dur": 299, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__12.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240164302, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Atomic.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240163886, "dur": 527, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240163565, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/wkyqrwuax58v.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240164489, "dur": 420, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppUnresolvedIndirectCallStubs.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240165171, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorState.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240165033, "dur": 387, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240164460, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/gamejsp186ls.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240165474, "dur": 138, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240165633, "dur": 247, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240165452, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/z9pbruiiaf9h.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240165881, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240166050, "dur": 142, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__5.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756725240166429, "dur": 239, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 5, "ts": 1756725240166197, "dur": 853, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756725240166037, "dur": 1013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/12risjq295wp.o"}}, {"pid": 12345, "tid": 5, "ts": 1756725240167069, "dur": 1356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240085549, "dur": 8667, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240094365, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240094961, "dur": 46230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240141198, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VRModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1756725240141380, "dur": 568, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VRModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1756725240141982, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240141966, "dur": 181, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240141193, "dur": 954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/hvbn2g7oqrkd.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240142147, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240142618, "dur": 168, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1756725240143017, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-object-internals.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240142798, "dur": 292, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240142574, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dv801ma8o6j7.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240143115, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kt28m9d4qr8r.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240143183, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240143308, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ekidu8rinvtg.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240143410, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dxspxxgh0vv7.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240143489, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240143612, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataRegistration.c"}}, {"pid": 12345, "tid": 6, "ts": 1756725240143715, "dur": 66, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240143611, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/uuz3kmg2qd45.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240143781, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240143965, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xtqqh5s42hu8.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240144080, "dur": 5605, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240149772, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Debug.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240149969, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Memory.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240150046, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-string-types.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240149690, "dur": 456, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240144065, "dur": 6082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kq7tsmgox9jt.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240150148, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240150332, "dur": 408, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240150745, "dur": 187, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240150321, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/cki4hef9wxxt.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240150932, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240151098, "dur": 247, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240151459, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/icalls/mscorlib/System.Runtime.InteropServices/Marshal.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240151630, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240152033, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/sparsetable.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240151354, "dur": 767, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240151080, "dur": 1042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/jt361qbkgt0c.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240152123, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240152302, "dur": 192, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubsystemsModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240152548, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/ArrayMetadata.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240152534, "dur": 441, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240152296, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xcsok1bovdy5.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240152976, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240153119, "dur": 307, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240153623, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_CappedSemaphore_FutexBased.inl.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240153813, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Gcc.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240153430, "dur": 750, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240153096, "dur": 1084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/wbs771ja3y1x.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240154181, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240154337, "dur": 350, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__4.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240154933, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Win32/ThreadLocalValueImpl.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240155087, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240154691, "dur": 521, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240154294, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/k00kyd2lz362.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240155213, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240155382, "dur": 109, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240155629, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/pch/pch-cpp.hpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240155495, "dur": 381, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240155366, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kro2mx67ky43.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240155876, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240156056, "dur": 119, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__11.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240156180, "dur": 377, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240156050, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/gdwj8guaxfld.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240156558, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240156829, "dur": 388, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240157230, "dur": 337, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240156784, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/vjrytgy8bkyo.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240157621, "dur": 270, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240158319, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-common.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240157901, "dur": 569, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240157592, "dur": 879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ehci2sptqv4o.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240158471, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240158727, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Core.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240158780, "dur": 280, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Core.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240159331, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-tiny.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240159478, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Il2CppError.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240159065, "dur": 593, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240158726, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/aagjx3ewa3od.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240159658, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240160024, "dur": 1524, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__8.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240162061, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/gc/GarbageCollector.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240161555, "dur": 758, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240160005, "dur": 2308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/rj838vqnopy3.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240162314, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240162513, "dur": 675, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__17.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240163503, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/ArchitectureDetection.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240163194, "dur": 636, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240162465, "dur": 1366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/pbdo7jyzda6c.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240163831, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240164042, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240164099, "dur": 414, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240164521, "dur": 276, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240164041, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/c551sjs20u8n.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240164844, "dur": 302, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppInvokerTable.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240165249, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Il2CppHashMap.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240165504, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/ReentrantLock.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240165803, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/CustomAttributeDataReader.h"}}, {"pid": 12345, "tid": 6, "ts": 1756725240165156, "dur": 1008, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240164823, "dur": 1342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/mnhpn23dm81g.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240166165, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240166368, "dur": 181, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756725240166555, "dur": 258, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240166346, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ixy0mnla4vs7.o"}}, {"pid": 12345, "tid": 6, "ts": 1756725240166813, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756725240167032, "dur": 1415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240085557, "dur": 8824, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240094382, "dur": 46767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240141184, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/__Generated_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1756725240141299, "dur": 714, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/__Generated_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1756725240142028, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240142016, "dur": 113, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240141183, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/38d148z9lmv1.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240142129, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240142472, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240142638, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1756725240142636, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/gd7muqzn3cdh.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240142809, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1756725240142883, "dur": 158, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1756725240142808, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/loahaolbunoh.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240143068, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240143208, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/lnmren2bwili.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240143281, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240143405, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dchdh2o4znbp.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240143459, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240143547, "dur": 87, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataUsage.c"}}, {"pid": 12345, "tid": 7, "ts": 1756725240143637, "dur": 66, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240143532, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/aq61n7ofibvq.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240143703, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240144023, "dur": 5647, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.XRModule.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240149854, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240150047, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextHash.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240149680, "dur": 660, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240143974, "dur": 6367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/v2y5ahl94kyj.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240150341, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240150461, "dur": 681, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__14.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240151146, "dur": 155, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240150448, "dur": 853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ziw1uo2yckiu.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240151301, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240151476, "dur": 100, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240151933, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Alignment.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240151581, "dur": 423, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240151450, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/j1gvjj936xrc.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240152040, "dur": 164, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240152541, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Baselib.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240152215, "dur": 559, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240152024, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/mh5ls678vavl.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240152775, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240153125, "dur": 318, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputModule.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240153453, "dur": 324, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240153083, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7ql4lkgd50sf.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240153825, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__1.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240153914, "dur": 219, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240153805, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/zsen5cjx4gp8.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240154133, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240154319, "dur": 152, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__6.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240154476, "dur": 187, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240154284, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/6nopy2wh6tz1.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240154664, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240154865, "dur": 123, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__1.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240155092, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Output.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240154992, "dur": 472, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240154849, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dx9riftspp2k.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240155465, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240155647, "dur": 80, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__3.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240155731, "dur": 152, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240155635, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dox96yxebk0l.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240155884, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240156312, "dur": 126, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__8.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240156445, "dur": 213, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240156289, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/h7e3t5s4iarn.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240156658, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240156818, "dur": 176, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__4.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240157132, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SystemFutex.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240157251, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Thread.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240157669, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Object.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240157000, "dur": 793, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240156793, "dur": 1000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/9v3ttr3c5t5p.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240157794, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240158040, "dur": 773, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__1.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240158836, "dur": 291, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240158020, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/lqxkm5l49q77.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240159133, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240159475, "dur": 189, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__1.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240159669, "dur": 448, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240159444, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/y4mlupycpoxb.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240160118, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240160321, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__6.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240160373, "dur": 1013, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__6.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240161420, "dur": 316, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240160320, "dur": 1416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/5vkz86vrhz54.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240161736, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240161912, "dur": 442, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__21.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240162838, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240162363, "dur": 889, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240161870, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/i7ulka6twmw7.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240163253, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240163432, "dur": 403, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__14.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240163840, "dur": 502, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240163411, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/i8lvrlp63v1u.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240164401, "dur": 250, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Mono.Security.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240165009, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Baselib.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240164656, "dur": 660, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240164374, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7t9dzw37r9x4.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240165317, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240165406, "dur": 191, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240165664, "dur": 276, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240165382, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/59qvfaqr8w89.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240165941, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240166071, "dur": 114, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__4.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240166429, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-il2cpp.h"}}, {"pid": 12345, "tid": 7, "ts": 1756725240166190, "dur": 416, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240166039, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/gnoucac56hl6.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240166606, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240166777, "dur": 455, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__1.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756725240167237, "dur": 125, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240166750, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/94engiq2zv51.o"}}, {"pid": 12345, "tid": 7, "ts": 1756725240167362, "dur": 834, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756725240168245, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240085554, "dur": 8821, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240094375, "dur": 46844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240141221, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1756725240141414, "dur": 514, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1756725240142020, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 8, "ts": 1756725240142007, "dur": 122, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240141220, "dur": 909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/h9o2e5pu21e7.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240142129, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240142391, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/3djan4kfho4r.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240142452, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240142615, "dur": 137, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1756725240142588, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/obujgxtk7azo.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240142783, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240142967, "dur": 92, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1756725240142952, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/3ginivtzwot6.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240143085, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240143291, "dur": 59, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240143215, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ux0ya736w0r4.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240143350, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240143413, "dur": 64, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Core_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1756725240143408, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ajs2rnhbkps5.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240143596, "dur": 89, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240143528, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ev1eavk34vm6.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240143686, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240143851, "dur": 81, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericClassTable.c"}}, {"pid": 12345, "tid": 8, "ts": 1756725240144024, "dur": 57, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240143835, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/pl5xll6g6bug.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240144081, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240144194, "dur": 5413, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__1.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240149611, "dur": 207, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240144146, "dur": 5672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/y2jpsxlsqjru.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240149819, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240149940, "dur": 148, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__9.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240150092, "dur": 158, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240149927, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/jn9wxco26b2b.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240150251, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240150436, "dur": 238, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__3.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240150679, "dur": 166, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240150398, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/poajx93em5nf.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240150846, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240151116, "dur": 90, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__10.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240151418, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/VerifyPlatformEnvironment.h"}}, {"pid": 12345, "tid": 8, "ts": 1756725240151470, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Timer.h"}}, {"pid": 12345, "tid": 8, "ts": 1756725240151211, "dur": 429, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240151074, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7z1kzgf8zh2s.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240151640, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240151790, "dur": 105, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__3.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240152032, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppTypeCompare.h"}}, {"pid": 12345, "tid": 8, "ts": 1756725240152108, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm/ClassInlines.h"}}, {"pid": 12345, "tid": 8, "ts": 1756725240151901, "dur": 449, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240151742, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/lpua9iot8z0w.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240152351, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240152545, "dur": 421, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240152977, "dur": 500, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240152516, "dur": 962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/s955usvj08sk.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240153507, "dur": 335, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240153846, "dur": 186, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240153495, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/k8skzjaez66r.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240154032, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240154190, "dur": 211, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240154405, "dur": 172, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240154185, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/n6sbgcqtwopv.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240154577, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240154732, "dur": 194, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__2.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240154944, "dur": 217, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240154721, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ycdgyo7psde4.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240155161, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240155361, "dur": 183, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__7.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240155555, "dur": 178, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240155338, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/56rq26k5nmgn.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240155733, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240156111, "dur": 352, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__1.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240156473, "dur": 451, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240156101, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/r094692i4t7m.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240156924, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240157031, "dur": 211, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__3.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240157248, "dur": 230, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240157018, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/og05dipf25t6.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240157479, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240157623, "dur": 262, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__5.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240158326, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Object.h"}}, {"pid": 12345, "tid": 8, "ts": 1756725240157894, "dur": 565, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240157611, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/t18tmjbwn7s5.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240158459, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240158664, "dur": 1142, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240159840, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tabledefs.h"}}, {"pid": 12345, "tid": 8, "ts": 1756725240160170, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Output.h"}}, {"pid": 12345, "tid": 8, "ts": 1756725240160735, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Posix/ThreadLocalValueImpl.h"}}, {"pid": 12345, "tid": 8, "ts": 1756725240159810, "dur": 1676, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240158637, "dur": 2849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xqtd6tes97r2.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240161487, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240161675, "dur": 502, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__3.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240162196, "dur": 418, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240161654, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/yst6s8my7k1m.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240162614, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240163049, "dur": 166, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__16.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240163502, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/ErrorCodes.h"}}, {"pid": 12345, "tid": 8, "ts": 1756725240163221, "dur": 588, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240163024, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/4v9zrak2amut.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240163811, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240164024, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__10.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240164077, "dur": 471, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__10.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240164554, "dur": 249, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240164023, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/axamr7zeqfgx.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240164803, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240165005, "dur": 239, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCodeRegistration.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240165274, "dur": 668, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240164959, "dur": 983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/gxlr9z8re4yc.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240166018, "dur": 163, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__6.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240166188, "dur": 466, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240165988, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/2aczcz60d8d5.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240166709, "dur": 186, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/GenericMethods.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756725240166929, "dur": 70, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 8, "ts": 1756725240167085, "dur": 209, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756725240166680, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/tq6qp8wvae1f.o"}}, {"pid": 12345, "tid": 8, "ts": 1756725240167305, "dur": 1180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756725240175812, "dur": 1216, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 4350, "tid": 1, "ts": 1756725240204553, "dur": 687, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend_profiler0.traceevents"}}, {"pid": 4350, "tid": 1, "ts": 1756725240205255, "dur": 558, "ph": "X", "name": "backend_profiler0.traceevents", "args": {}}, {"pid": 4350, "tid": 1, "ts": 1756725240202739, "dur": 3760, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"pid": 4350, "tid": 1, "ts": 1756725240234911, "dur": 3075, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}