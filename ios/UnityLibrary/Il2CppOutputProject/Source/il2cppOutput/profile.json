{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {"os": "Unix 15.4.0", "cpuCount": "8", "mappedPhysicalMemory": "42mb", "commandLine": "\"/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/build/deploy_arm64/il2cpp.dll\" --compile-cpp --platform=iOS \"--baselib-directory=/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Libraries\" --additional-defines=IL2CPP_DEBUG=0 --incremental-g-c-time-slice=3 --dotnetprofile=unityaot-macos --profiler-report --print-command-line \"--external-lib-il2-cpp=/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Libraries/libil2cpp.a\" --generatedcppdir=Il2CppOutputProject/Source/il2cppOutput --architecture=arm64 \"--outputpath=/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/build/ios/Debug-iphoneos/libGameAssembly.a\" --cachedirectory=/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-hbfggqwtkgawgeadtkjuokuewdvk/Build/Intermediates.noindex/Unity-iPhone.build/Debug-iphoneos/artifacts/arm64 --configuration=Debug"}, "traceEvents": [{"pid": 91392, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "il2cpp_outer"}}, {"pid": 91392, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 91392, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen2"}}, {"pid": 91392, "tid": 1, "ts": 1756719641122498, "dur": 1142, "ph": "X", "name": "GC - Gen2", "args": {}}, {"pid": 91392, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen1"}}, {"pid": 91392, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen0"}}, {"pid": 91392, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": "Main thread"}}, {"pid": 91392, "tid": 1, "ts": 1756719640621000, "dur": 481176, "ph": "X", "name": "il2cpp.exe", "args": {"analytics": "1"}}, {"pid": 91392, "tid": 1, "ts": 1756719640622534, "dur": 50859, "ph": "X", "name": "ParseArguments", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719640673395, "dur": 7957, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719640693789, "dur": 395848, "ph": "X", "name": "BeeDriverRunner.ExecuteIl2Cpp", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719641092484, "dur": 8249, "ph": "X", "name": "Write Analytics", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719641102177, "dur": 12167, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719641116844, "dur": 1261, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 91392, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 91392, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 91392, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": "Main thread"}}, {"pid": 91392, "tid": 1, "ts": 1756719640780466, "dur": 302703, "ph": "X", "name": "Build FinalProgram", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719640782241, "dur": 8608, "ph": "X", "name": "Writing build program input data", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719640790918, "dur": 292182, "ph": "X", "name": "Running build system backend", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719641083126, "dur": 42, "ph": "X", "name": "Finishing", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719641096042, "dur": 1321, "ph": "X", "name": "Main thread", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756719640844130, "dur": 7341, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756719640851633, "dur": 6227, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756719640857890, "dur": 309, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756719640858233, "dur": 1299, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756719640859532, "dur": 77525, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756719640937127, "dur": 134859, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756719640858248, "dur": 1288, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640859537, "dur": 46979, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640906517, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText zmq4/_dummy_for_header_discovery"}}, {"pid": 12345, "tid": 1, "ts": 1756719640907016, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640907324, "dur": 13869, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640921526, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_Lock_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640921644, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-common.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640922077, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640922188, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/gc/GarbageCollector.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640921205, "dur": 1113, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640907289, "dur": 15030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ispzi233l2l5.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640922348, "dur": 188, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640922608, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Atomic.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640922710, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringUtils.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640922766, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_ReentrantLock.inl.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640922544, "dur": 676, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640922336, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/poajx93em5nf.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640923221, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640923537, "dur": 281, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640924121, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/VerifyPlatformEnvironment.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640923828, "dur": 443, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640923519, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/09qmt99v5bpd.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640924271, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640924417, "dur": 383, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640924813, "dur": 378, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640924399, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/x43r6gw37lxs.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640925234, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640925284, "dur": 116, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640925407, "dur": 341, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640925233, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/k8hblmxu740r.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640925749, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640925916, "dur": 107, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__6.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640926027, "dur": 135, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640925895, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/6nopy2wh6tz1.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640926176, "dur": 84, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640926264, "dur": 129, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640926171, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/k00kyd2lz362.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640926393, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640926533, "dur": 223, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__5.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640926765, "dur": 247, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640926511, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/zble7nsaiatk.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640927012, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640927141, "dur": 178, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640927323, "dur": 207, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640927130, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/s8tlrpjh8vo2.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640927530, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640927723, "dur": 96, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__9.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640928122, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppTypeHash.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640928310, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_WakeupFallbackStrategy.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640927823, "dur": 690, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640927710, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/2mir9e71ly26.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640928514, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640928650, "dur": 90, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__6.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640928843, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/BasicTypes.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640929114, "dur": 516, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/WaitStatus-c-api.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640928746, "dur": 999, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640928624, "dur": 1121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ehci2sptqv4o.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640929745, "dur": 665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640930426, "dur": 191, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640930662, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640930623, "dur": 629, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640930410, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ksaquet8veqa.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640931253, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640931428, "dur": 363, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640932084, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640932746, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/densehashtable.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640931876, "dur": 1172, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640931387, "dur": 1661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/yst6s8my7k1m.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640933048, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640933217, "dur": 284, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__13.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640933704, "dur": 324, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Event.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640933506, "dur": 656, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640933188, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/h01e7587ztkf.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640934220, "dur": 241, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppUnresolvedIndirectCallStubs.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640934782, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-il2cpp.h"}}, {"pid": 12345, "tid": 1, "ts": 1756719640934471, "dur": 898, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640934181, "dur": 1188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/gamejsp186ls.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640935370, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640935576, "dur": 130, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__5.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640935711, "dur": 160, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640935547, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/12risjq295wp.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640935872, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640936012, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640936062, "dur": 114, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1756719640936180, "dur": 174, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640936011, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/94engiq2zv51.o"}}, {"pid": 12345, "tid": 1, "ts": 1756719640936355, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756719640936500, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640858248, "dur": 1293, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640859591, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640860296, "dur": 46291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640906589, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1756719640906745, "dur": 778, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1756719640907552, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640907528, "dur": 109, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640906588, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/h9o2e5pu21e7.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640907638, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640907785, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640907866, "dur": 248, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1756719640907850, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/i30949sfyigv.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640908213, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640908285, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/3ginivtzwot6.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640908387, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640908500, "dur": 87, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640908456, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/sfvvjoqxbb29.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640908588, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640908758, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppTypeDefinitions.c"}}, {"pid": 12345, "tid": 2, "ts": 1756719640908825, "dur": 115, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppTypeDefinitions.c"}}, {"pid": 12345, "tid": 2, "ts": 1756719640908757, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/msowb4nh0fl7.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640908975, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640909186, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640909126, "dur": 169, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640909095, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/s6fd6wynm3kn.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640909295, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640909387, "dur": 12003, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/__Generated.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640921489, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/sparsehashtable.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640921613, "dur": 447, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm/String.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640922080, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppSignature.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640922190, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640921407, "dur": 1082, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640909353, "dur": 13136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/3rlsr07jdq2v.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640922523, "dur": 474, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__2.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640923087, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_CappedSemaphore_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640923008, "dur": 462, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640922513, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/486uh0phjhcw.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640923470, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640923599, "dur": 207, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__1.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640924077, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_WakeupFallbackStrategy.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640923816, "dur": 366, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640923580, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/funyd0vy2ndu.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640924183, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640924411, "dur": 504, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640924920, "dur": 423, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640924383, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/y0z7393wloop.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640925344, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640925495, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640925600, "dur": 99, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640925705, "dur": 232, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640925494, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/9okv497w31pj.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640925964, "dur": 85, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__5.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640926053, "dur": 132, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640925950, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/5f48dngj2rim.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640926204, "dur": 78, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__3.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640926286, "dur": 131, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640926194, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/jsg4dylnv1pn.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640926430, "dur": 396, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__1.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640927022, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Atomic_Macros.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640926838, "dur": 392, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640926426, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dx9riftspp2k.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640927230, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640927437, "dur": 115, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640927558, "dur": 194, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640927417, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/saat1sglwppb.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640927752, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640927927, "dur": 397, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__6.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640928331, "dur": 265, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640927892, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/vjrytgy8bkyo.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640928597, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640928817, "dur": 202, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__4.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640929055, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Gcc_Patch_PostInclude.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640929114, "dur": 514, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/ArrayMetadata.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640929629, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640929818, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppTypeCompare.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640929030, "dur": 992, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640928793, "dur": 1229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/stod23uxj5bq.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640930022, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640930462, "dur": 161, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__2.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640930627, "dur": 234, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640930435, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/jkq99ycasc9u.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640930862, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640931037, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__9.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640931089, "dur": 489, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__9.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640932080, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_Lock_FutexBased.inl.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640931609, "dur": 617, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640931036, "dur": 1190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/czbso54krdan.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640932226, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640932383, "dur": 526, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__2.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640932919, "dur": 223, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640932362, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ddqidz804wp3.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640933142, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640933269, "dur": 226, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__12.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640933703, "dur": 318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_Lock_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640933499, "dur": 673, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640933235, "dur": 937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/wkyqrwuax58v.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640934247, "dur": 128, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppReversePInvokeWrapperTable.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640934380, "dur": 192, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640934210, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/f8c7sw7hc9jd.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640934572, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640934755, "dur": 227, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCFieldValuesTable.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640935309, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SourceLocation.h"}}, {"pid": 12345, "tid": 2, "ts": 1756719640934991, "dur": 709, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640934725, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/o2aouj7qt9vq.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640935701, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640935865, "dur": 542, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__3.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1756719640936411, "dur": 203, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640935860, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/v7k6w0x73t9m.o"}}, {"pid": 12345, "tid": 2, "ts": 1756719640936615, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756719640936734, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640858258, "dur": 1349, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640859607, "dur": 46948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640906558, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756719640906744, "dur": 743, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756719640907546, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 3, "ts": 1756719640907530, "dur": 112, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640906556, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ltgue0fsouv2.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640907643, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640907931, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756719640908121, "dur": 65, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640907930, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/yfn7vq7n2ssz.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640908186, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640908287, "dur": 95, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756719640908281, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kob167w4iqs3.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640908483, "dur": 53, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756719640908466, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/55p7gi2n894a.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640908595, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640908778, "dur": 126, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Configuration_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756719640908907, "dur": 114, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640908735, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xht2hp52y26b.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640909023, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640909186, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756719640909314, "dur": 149, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1756719640909563, "dur": 195, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640909185, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/eq6bhhidvzcc.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640909758, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640909917, "dur": 11217, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640921194, "dur": 249, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640909878, "dur": 11566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/tpeqsxory6ti.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640921445, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640921622, "dur": 535, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__6.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640922182, "dur": 235, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640921599, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/cki4hef9wxxt.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640922418, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640922714, "dur": 110, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__10.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640922828, "dur": 230, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640922689, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7z1kzgf8zh2s.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640923059, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640923225, "dur": 234, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640923466, "dur": 347, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640923220, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dizc0yequ1zd.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640923814, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640924086, "dur": 105, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640924196, "dur": 355, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640924069, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/s955usvj08sk.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640924552, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640924616, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640924740, "dur": 278, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640925025, "dur": 294, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640924615, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/nf366svp41cr.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640925319, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640925506, "dur": 112, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640925799, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-common.h"}}, {"pid": 12345, "tid": 3, "ts": 1756719640925622, "dur": 426, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640925477, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/n6sbgcqtwopv.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640926048, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640926541, "dur": 205, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__6.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640926758, "dur": 250, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640926504, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kro2mx67ky43.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640927008, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640927134, "dur": 499, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__3.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640927922, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/libc_allocator_with_realloc.h"}}, {"pid": 12345, "tid": 3, "ts": 1756719640927637, "dur": 495, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640927129, "dur": 1003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dox96yxebk0l.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640928132, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640928296, "dur": 122, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__2.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640928631, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm/ClassInlines.h"}}, {"pid": 12345, "tid": 3, "ts": 1756719640928424, "dur": 490, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640928277, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/30why8aizhin.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640928914, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640929070, "dur": 1008, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__1.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640930082, "dur": 123, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640929047, "dur": 1158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/lqxkm5l49q77.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640930205, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640930330, "dur": 254, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__4.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640930594, "dur": 241, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640930306, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/57deao75y21t.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640930835, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640930985, "dur": 623, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640932081, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericInstHash.h"}}, {"pid": 12345, "tid": 3, "ts": 1756719640931619, "dur": 668, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640930961, "dur": 1326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/t8gi1ceuhem8.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640932287, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640932413, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__19.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640932463, "dur": 374, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__19.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640932842, "dur": 197, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640932412, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/bz0s78svk0yb.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640933039, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640933175, "dur": 597, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__15.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640933778, "dur": 168, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640933157, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/3npj7u336jde.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640933946, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640934077, "dur": 242, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Mono.Security__1.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640934324, "dur": 162, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640934061, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/cx1eyqer81ig.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640934487, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640934637, "dur": 187, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppInteropDataTable.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640934830, "dur": 177, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640934631, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/pf317lrw3uwk.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640935008, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640935189, "dur": 208, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues2.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640935571, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include/BaselibPlatformSpecificEnvironment.h"}}, {"pid": 12345, "tid": 3, "ts": 1756719640935403, "dur": 383, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640935164, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/z9pbruiiaf9h.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640935786, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640935959, "dur": 217, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__1.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1756719640936180, "dur": 173, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640935939, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/9jx1wb3bx3go.o"}}, {"pid": 12345, "tid": 3, "ts": 1756719640936353, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756719640936483, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640858251, "dur": 1347, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640859598, "dur": 46925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640906530, "dur": 413, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/__Generated_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1756719640906943, "dur": 561, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/__Generated_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1756719640907581, "dur": 60, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640906528, "dur": 1113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/38d148z9lmv1.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640907641, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640907888, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubsystemsModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1756719640907958, "dur": 161, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubsystemsModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1756719640907887, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/18qoqjfp2344.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640908151, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640908265, "dur": 96, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640908231, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/wc6xn0rnm3rx.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640908361, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640908554, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 4, "ts": 1756719640908496, "dur": 121, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640908454, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ux0ya736w0r4.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640908617, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640908771, "dur": 96, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1756719640908872, "dur": 131, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640908743, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dxspxxgh0vv7.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640909004, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640909168, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericClassTable.c"}}, {"pid": 12345, "tid": 4, "ts": 1756719640909280, "dur": 222, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericClassTable.c"}}, {"pid": 12345, "tid": 4, "ts": 1756719640909557, "dur": 119, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640909167, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/pl5xll6g6bug.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640909677, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640909793, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__2.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640909844, "dur": 11268, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__2.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640921259, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericMethodHash.h"}}, {"pid": 12345, "tid": 4, "ts": 1756719640921118, "dur": 486, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640909792, "dur": 11813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/jind86zpt72d.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640921605, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640922084, "dur": 124, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__5.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640922213, "dur": 236, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640922066, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/bcynxyyatvge.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640922449, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640922613, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__11.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640922696, "dur": 174, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__11.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640922877, "dur": 272, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640922612, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/sf58eqt525dc.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640923149, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640923294, "dur": 162, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640923463, "dur": 312, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640923282, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/j1gvjj936xrc.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640923775, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640923914, "dur": 217, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SubsystemsModule.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640924136, "dur": 209, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640923904, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xcsok1bovdy5.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640924345, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640924532, "dur": 310, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640925278, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/sparsetable.h"}}, {"pid": 12345, "tid": 4, "ts": 1756719640924853, "dur": 525, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640924483, "dur": 896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/wbs771ja3y1x.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640925431, "dur": 145, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640925587, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/FastReaderReaderWriterLock.h"}}, {"pid": 12345, "tid": 4, "ts": 1756719640925798, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppTypeCompare.h"}}, {"pid": 12345, "tid": 4, "ts": 1756719640925582, "dur": 446, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640925404, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/scqzmfkaf599.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640926029, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640926475, "dur": 244, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__8.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640926732, "dur": 257, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640926429, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/6ec7mm13rox2.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640926989, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640927186, "dur": 158, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__11.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640927350, "dur": 410, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640927160, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/gdwj8guaxfld.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640927760, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640927917, "dur": 274, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__5.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640928496, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 4, "ts": 1756719640928633, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/libc_allocator_with_realloc.h"}}, {"pid": 12345, "tid": 4, "ts": 1756719640928196, "dur": 557, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640927902, "dur": 852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/a4nae7jb9vm5.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640928754, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640928972, "dur": 648, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__2.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640929623, "dur": 214, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640928952, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/myh16k22dton.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640929837, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640930318, "dur": 311, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Core.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640930658, "dur": 59, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/dummy"}}, {"pid": 12345, "tid": 4, "ts": 1756719640930717, "dur": 528, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640930295, "dur": 951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/aagjx3ewa3od.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640931246, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640931362, "dur": 484, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__5.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640931863, "dur": 342, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640931322, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kghbtxhuebul.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640932205, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640932365, "dur": 435, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__20.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640932805, "dur": 190, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640932351, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xrrx40rwtyko.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640932995, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640933169, "dur": 707, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__16.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640934067, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_WakeupFallbackStrategy.h"}}, {"pid": 12345, "tid": 4, "ts": 1756719640934258, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Atomic.h"}}, {"pid": 12345, "tid": 4, "ts": 1756719640933880, "dur": 713, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640933156, "dur": 1437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/4v9zrak2amut.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640934593, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640934717, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCTypeValuesTable.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640934797, "dur": 120, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCTypeValuesTable.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640934922, "dur": 220, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640934716, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ykjf64ve6s6b.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640935142, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640935266, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640935318, "dur": 130, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640935454, "dur": 236, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640935264, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dbo5l25id83i.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640935691, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640935824, "dur": 280, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__4.cpp"}}, {"pid": 12345, "tid": 4, "ts": 1756719640936109, "dur": 164, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640935820, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/gnoucac56hl6.o"}}, {"pid": 12345, "tid": 4, "ts": 1756719640936273, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756719640936545, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640858251, "dur": 1343, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640859595, "dur": 46923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640906518, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText zmq4/libGameAssembly.objectfiles"}}, {"pid": 12345, "tid": 5, "ts": 1756719640907016, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640907300, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1756719640907419, "dur": 148, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1756719640907588, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 5, "ts": 1756719640907572, "dur": 74, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640907299, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/0y21cnyhbavb.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640907647, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640907951, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 5, "ts": 1756719640907851, "dur": 205, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640907820, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/s4dvg17s8zv3.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640908056, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640908227, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/loahaolbunoh.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640908293, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640908436, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640908532, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/lh4lyk90achf.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640908606, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640908735, "dur": 109, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Core_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1756719640908727, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ajs2rnhbkps5.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640908975, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/aq61n7ofibvq.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640909027, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640909146, "dur": 141, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericInstDefinitions.c"}}, {"pid": 12345, "tid": 5, "ts": 1756719640909374, "dur": 126, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640909140, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/t1lbx9mqbuee.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640909501, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640909710, "dur": 11401, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__3.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640921136, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config-api.h"}}, {"pid": 12345, "tid": 5, "ts": 1756719640921118, "dur": 348, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640909673, "dur": 11793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kq7tsmgox9jt.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640921510, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__7.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640921577, "dur": 561, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__7.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640922187, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringUtils.h"}}, {"pid": 12345, "tid": 5, "ts": 1756719640922144, "dur": 406, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640921509, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xzlsxnt6zl9c.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640922622, "dur": 507, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__12.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640923139, "dur": 364, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640922574, "dur": 930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dv8wuz725tpv.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640923504, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640923639, "dur": 152, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640923989, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Assembly.h"}}, {"pid": 12345, "tid": 5, "ts": 1756719640924077, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/os/Event.h"}}, {"pid": 12345, "tid": 5, "ts": 1756719640923799, "dur": 457, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640923626, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/wmbaxi9di32s.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640924257, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640924523, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640924643, "dur": 197, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640924846, "dur": 221, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640924522, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/461ot2bidqkq.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640925067, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640925268, "dur": 132, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640925499, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/vm/String.h"}}, {"pid": 12345, "tid": 5, "ts": 1756719640925585, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/PlatformEnvironment.h"}}, {"pid": 12345, "tid": 5, "ts": 1756719640925407, "dur": 456, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640925226, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/3l87mr34ulq7.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640925863, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640926444, "dur": 364, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__9.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640926819, "dur": 266, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640926429, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/rufv2vf1uc9l.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640927086, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640927246, "dur": 124, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__10.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640927374, "dur": 214, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640927211, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/vxsvs7fdur9t.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640927588, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640927848, "dur": 569, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__7.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640928423, "dur": 269, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640927826, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/wrbfewe7fmf4.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640928692, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640928848, "dur": 764, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__3.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640929626, "dur": 68, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 5, "ts": 1756719640929705, "dur": 231, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640928830, "dur": 1106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7dlpmug0gh06.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640929936, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640930169, "dur": 195, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640930370, "dur": 258, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640930165, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/pnchmmpn7x4p.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640930672, "dur": 134, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__1.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640930811, "dur": 366, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640930652, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/y4mlupycpoxb.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640931177, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640931368, "dur": 501, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__4.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640931873, "dur": 306, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640931329, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/z0dndo3wbni2.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640932179, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640932324, "dur": 504, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__21.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640932836, "dur": 195, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640932319, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/i7ulka6twmw7.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640933032, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640933227, "dur": 562, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__14.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640934254, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SystemSemaphore.h"}}, {"pid": 12345, "tid": 5, "ts": 1756719640933794, "dur": 730, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640933184, "dur": 1340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/i8lvrlp63v1u.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640934568, "dur": 250, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppInvokerTable.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640934823, "dur": 182, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640934544, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/mnhpn23dm81g.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640935005, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640935219, "dur": 270, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues1.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640935499, "dur": 264, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640935198, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/y15u6e2j6cs2.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640935764, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640935902, "dur": 446, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__2.cpp"}}, {"pid": 12345, "tid": 5, "ts": 1756719640936545, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericInstCompare.h"}}, {"pid": 12345, "tid": 5, "ts": 1756719640936354, "dur": 294, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640935891, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/uz0pclh9zrs7.o"}}, {"pid": 12345, "tid": 5, "ts": 1756719640936648, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756719640936775, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640858253, "dur": 1348, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640859601, "dur": 46933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640906536, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VRModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1756719640906655, "dur": 838, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VRModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1756719640907588, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 6, "ts": 1756719640907571, "dur": 97, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640906534, "dur": 1134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/hvbn2g7oqrkd.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640907668, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640907918, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1756719640908128, "dur": 56, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640907917, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/dv801ma8o6j7.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640908263, "dur": 72, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640908230, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7nahgss95by3.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640908335, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640908568, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 6, "ts": 1756719640908537, "dur": 103, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640908451, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/lnmren2bwili.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640908641, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640908747, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Mono.Security_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1756719640908802, "dur": 69, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Mono.Security_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1756719640908904, "dur": 91, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640908746, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7ud7tqeckn3w.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640908996, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640909176, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericAdjustorThunkTable.c"}}, {"pid": 12345, "tid": 6, "ts": 1756719640909291, "dur": 205, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericAdjustorThunkTable.c"}}, {"pid": 12345, "tid": 6, "ts": 1756719640909500, "dur": 65, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640909175, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ld0en8g94llk.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640909565, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640909910, "dur": 11204, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640921120, "dur": 188, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640909870, "dur": 11439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/y2jpsxlsqjru.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640921309, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640921430, "dur": 649, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__9.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640922152, "dur": 381, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640921405, "dur": 1129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/jn9wxco26b2b.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640922535, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640922706, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640922782, "dur": 98, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640922886, "dur": 259, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640922705, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/jt361qbkgt0c.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640923145, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640923316, "dur": 229, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640923549, "dur": 284, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640923303, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/8napw69po3ot.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640923833, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640923997, "dur": 133, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640924134, "dur": 252, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640923978, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ii1nxgsvxhig.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640924435, "dur": 371, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640924816, "dur": 221, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640924425, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7ql4lkgd50sf.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640925037, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640925218, "dur": 179, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640925584, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppSignature.h"}}, {"pid": 12345, "tid": 6, "ts": 1756719640925405, "dur": 448, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640925189, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/k8skzjaez66r.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640925853, "dur": 537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640926395, "dur": 254, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__2.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640926665, "dur": 278, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640926391, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ycdgyo7psde4.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640926943, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640927111, "dur": 538, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__4.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640927658, "dur": 326, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640927106, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/y30xm8bce8b5.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640928029, "dur": 182, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__4.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640928215, "dur": 237, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640928007, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/9v3ttr3c5t5p.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640928452, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640928587, "dur": 320, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640929108, "dur": 511, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Gcc_Patch.h"}}, {"pid": 12345, "tid": 6, "ts": 1756719640928915, "dur": 745, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640928571, "dur": 1091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/2sc1dtujvf17.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640929662, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640929824, "dur": 82, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640929910, "dur": 148, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640929802, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/mvkh8vqeh0sp.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640930058, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640930312, "dur": 417, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640930741, "dur": 462, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640930285, "dur": 918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xqtd6tes97r2.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640931203, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640931289, "dur": 665, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640931958, "dur": 258, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640931281, "dur": 936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/5vkz86vrhz54.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640932217, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640932326, "dur": 545, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__22.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640933406, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen.h"}}, {"pid": 12345, "tid": 6, "ts": 1756719640932881, "dur": 661, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640932314, "dur": 1228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/3my9zjkrza8q.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640933543, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640933683, "dur": 149, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640933837, "dur": 174, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640933665, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/c551sjs20u8n.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640934052, "dur": 318, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640934375, "dur": 197, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640934035, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/b916rvhibrfz.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640934573, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640934685, "dur": 218, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCodeRegistration.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640934910, "dur": 328, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640934669, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/gxlr9z8re4yc.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640935301, "dur": 159, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640935467, "dur": 313, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640935280, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/2aczcz60d8d5.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640935781, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640936031, "dur": 147, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1756719640936182, "dur": 179, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640935982, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/u7ac3yv4h71v.o"}}, {"pid": 12345, "tid": 6, "ts": 1756719640936361, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756719640936477, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640858254, "dur": 1349, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640859603, "dur": 46924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640906559, "dur": 923, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1756719640907545, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 7, "ts": 1756719640907527, "dur": 117, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640906528, "dur": 1116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ni62wnjjywqn.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640907645, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640907937, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1756719640908041, "dur": 73, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1756719640907936, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/obujgxtk7azo.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640908147, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640908331, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kvo3hg0e7zyt.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640908389, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640908516, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1756719640908514, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ekidu8rinvtg.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640908607, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640908762, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppRgctxTable.c"}}, {"pid": 12345, "tid": 7, "ts": 1756719640908819, "dur": 176, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppRgctxTable.c"}}, {"pid": 12345, "tid": 7, "ts": 1756719640908761, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ev1eavk34vm6.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640909019, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640909323, "dur": 91, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640909136, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/6rud99vuiqta.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640909492, "dur": 11723, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.VRModule.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640921645, "dur": 433, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorCode.h"}}, {"pid": 12345, "tid": 7, "ts": 1756719640921226, "dur": 852, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640909460, "dur": 12618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xaf043wt1g6r.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640922079, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640922199, "dur": 111, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__4.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640922316, "dur": 224, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640922179, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/qwuzhpkdq74u.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640922600, "dur": 219, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__13.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640922823, "dur": 226, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640922565, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/e3sdffo19v6a.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640923097, "dur": 106, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640923419, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/type_traits.h"}}, {"pid": 12345, "tid": 7, "ts": 1756719640923207, "dur": 481, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640923077, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kxd80fwss097.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640923689, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640923868, "dur": 138, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640924078, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_CappedSemaphore_SemaphoreBased.inl.h"}}, {"pid": 12345, "tid": 7, "ts": 1756719640924013, "dur": 447, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640923863, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/mh5ls678vavl.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640924516, "dur": 311, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule__1.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640924834, "dur": 223, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640924503, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/f4ttkhe820ko.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640925057, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640925273, "dur": 339, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__1.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640925801, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/dynamic_array.h"}}, {"pid": 12345, "tid": 7, "ts": 1756719640925616, "dur": 350, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640925244, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/zsen5cjx4gp8.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640925967, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640926432, "dur": 387, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640926829, "dur": 258, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640926427, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ucnctn1ynn63.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640927088, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640927260, "dur": 86, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__1.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640927351, "dur": 206, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640927228, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/r094692i4t7m.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640927558, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640928057, "dur": 162, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__3.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640928495, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorState.h"}}, {"pid": 12345, "tid": 7, "ts": 1756719640928223, "dur": 447, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640928044, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/og05dipf25t6.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640928670, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640928823, "dur": 833, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml__5.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640929659, "dur": 204, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640928775, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/t18tmjbwn7s5.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640929864, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640930279, "dur": 355, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Numerics.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640930654, "dur": 224, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640930274, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/5cl2vu2ddnxj.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640930878, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640931065, "dur": 1077, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__8.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640932145, "dur": 181, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640931045, "dur": 1282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/rj838vqnopy3.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640932327, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640932492, "dur": 374, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__17.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640932870, "dur": 203, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640932470, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/pbdo7jyzda6c.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640933073, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640933300, "dur": 601, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__11.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640933910, "dur": 62, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/dummy"}}, {"pid": 12345, "tid": 7, "ts": 1756719640934071, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Compiler/Baselib_Atomic_Msvc.h"}}, {"pid": 12345, "tid": 7, "ts": 1756719640934254, "dur": 284, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Il2CppHashMap.h"}}, {"pid": 12345, "tid": 7, "ts": 1756719640933972, "dur": 938, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640933258, "dur": 1653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/b5u2bngduhxc.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640934912, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640935088, "dur": 341, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640935560, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 7, "ts": 1756719640935439, "dur": 416, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640935060, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/59qvfaqr8w89.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640935856, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640936022, "dur": 348, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Generics.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640936545, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/pch/pch-cpp.hpp"}}, {"pid": 12345, "tid": 7, "ts": 1756719640936380, "dur": 312, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640935979, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ixy0mnla4vs7.o"}}, {"pid": 12345, "tid": 7, "ts": 1756719640936692, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756719640936925, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640858254, "dur": 1351, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640859605, "dur": 46926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640906533, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.XRModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1756719640906744, "dur": 780, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.XRModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1756719640907567, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 8, "ts": 1756719640907536, "dur": 121, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640906532, "dur": 1125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/39spirhah7t1.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640907657, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640907940, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config-api.h"}}, {"pid": 12345, "tid": 8, "ts": 1756719640907871, "dur": 189, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640907826, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/cgmbvz4k17kf.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640908134, "dur": 59, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640908087, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/gd7muqzn3cdh.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640908194, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640908292, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/kt28m9d4qr8r.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640908384, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640908510, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1756719640908565, "dur": 61, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1756719640908509, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ilrc8krxz2lu.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640908646, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640908785, "dur": 125, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1756719640908744, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/n30c0tr66gr5.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640908950, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640909080, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/uuz3kmg2qd45.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640909137, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640909482, "dur": 11641, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.XRModule.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640921139, "dur": 227, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640909436, "dur": 11930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/v2y5ahl94kyj.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640921366, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640921488, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__8.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640921562, "dur": 583, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__8.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640922152, "dur": 261, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640921487, "dur": 926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ifpgdx7329kw.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640922413, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640922583, "dur": 359, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__14.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640922953, "dur": 273, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640922553, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/ziw1uo2yckiu.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640923227, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640923434, "dur": 148, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__3.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640923602, "dur": 221, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640923402, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/lpua9iot8z0w.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640923823, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640923964, "dur": 164, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640924131, "dur": 213, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640923949, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/xljoc6rux3e4.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640924406, "dur": 953, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640925371, "dur": 288, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640924369, "dur": 1290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/oiz5l1ysv3hf.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640925659, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640925810, "dur": 647, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640926496, "dur": 304, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Unity.InputSystem__7.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640927022, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/PlatformDetection.h"}}, {"pid": 12345, "tid": 8, "ts": 1756719640926813, "dur": 460, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640926457, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/56rq26k5nmgn.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640927273, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640927797, "dur": 194, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__8.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640928009, "dur": 322, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640927793, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/h7e3t5s4iarn.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640928331, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640928503, "dur": 156, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System__10.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640928663, "dur": 329, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640928487, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/srpmh3ozgc65.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640928992, "dur": 626, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640929635, "dur": 99, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640929820, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/sparsehashtable.h"}}, {"pid": 12345, "tid": 8, "ts": 1756719640929971, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_CountdownTimer.h"}}, {"pid": 12345, "tid": 8, "ts": 1756719640929741, "dur": 358, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640929618, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/i2hc1muj0ktj.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640930099, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640930324, "dur": 499, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/System.Configuration.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640930833, "dur": 416, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640930302, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/hk5kkq7jonka.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640931269, "dur": 480, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__7.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640932080, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-object-internals.h"}}, {"pid": 12345, "tid": 8, "ts": 1756719640931787, "dur": 519, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640931260, "dur": 1046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/634ftmcql1mk.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640932306, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640932481, "dur": 654, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__18.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640933139, "dur": 397, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640932447, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/argcntcxgyhw.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640933536, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640933654, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__10.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640933715, "dur": 117, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/mscorlib__10.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640933836, "dur": 177, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640933654, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/axamr7zeqfgx.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640934014, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640934151, "dur": 234, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Mono.Security.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640934390, "dur": 231, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640934130, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/7t9dzw37r9x4.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640934621, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640934785, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues1.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640935034, "dur": 385, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues1.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640935425, "dur": 239, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640934784, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/2j9wnqe5s33o.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640935664, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640935989, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/GenericMethods.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640936040, "dur": 340, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/ios/UnityLibrary/Il2CppOutputProject/Source/il2cppOutput/GenericMethods.cpp"}}, {"pid": 12345, "tid": 8, "ts": 1756719640936390, "dur": 218, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756719640935988, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 zmq4/tq6qp8wvae1f.o"}}, {"pid": 12345, "tid": 8, "ts": 1756719640936624, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756719641073918, "dur": 2136, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 91392, "tid": 1, "ts": 1756719641097584, "dur": 670, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend_profiler0.traceevents"}}, {"pid": 91392, "tid": 1, "ts": 1756719641098276, "dur": 1700, "ph": "X", "name": "backend_profiler0.traceevents", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719641095597, "dur": 5101, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719641124554, "dur": 1922, "ph": "X", "name": "profile-bee.traceevents", "args": {}}, {"pid": 91392, "tid": 1, "ts": 1756719641122203, "dur": 4302, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}